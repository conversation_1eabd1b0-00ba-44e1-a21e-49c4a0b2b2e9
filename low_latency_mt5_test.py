#!/usr/bin/env python3
"""
Low-Latency MT5 Connection Test
Minimalistic script with kernel bypass, TCP_NODELAY, and FIX API wrapper
For testing actual execution delays vs theoretical 922.8ms
"""

import MetaTrader5 as mt5
import socket
import time
import threading
import queue
import ctypes
import os
import sys
from datetime import datetime
import numpy as np

# Low-latency socket optimizations
def optimize_socket(sock):
    """Apply low-latency socket optimizations"""
    try:
        # TCP_NODELAY - disable <PERSON><PERSON>'s algorithm
        sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        
        # SO_REUSEADDR - reuse address
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        # Set socket buffer sizes
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)
        
        # Set socket priority (Linux)
        if hasattr(socket, 'SO_PRIORITY'):
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_PRIORITY, 6)
            
        print("Socket optimizations applied successfully")
        return True
    except Exception as e:
        print(f"Socket optimization warning: {e}")
        return False

# Kernel bypass techniques
def set_process_priority():
    """Set high process priority for low latency"""
    try:
        if os.name == 'nt':  # Windows
            import psutil
            p = psutil.Process()
            p.nice(psutil.HIGH_PRIORITY_CLASS)
            print("Process priority set to HIGH")
        else:  # Linux
            os.nice(-10)
            print("Process nice value set to -10")
    except Exception as e:
        print(f"Priority setting warning: {e}")

def set_cpu_affinity():
    """Pin process to specific CPU cores"""
    try:
        if os.name == 'nt':
            import psutil
            p = psutil.Process()
            # Pin to first 2 cores
            p.cpu_affinity([0, 1])
            print("CPU affinity set to cores 0,1")
    except Exception as e:
        print(f"CPU affinity warning: {e}")

class LowLatencyMT5:
    """Low-latency MT5 wrapper with FIX API simulation"""
    
    def __init__(self):
        self.connected = False
        self.execution_times = []
        self.order_queue = queue.Queue()
        self.symbol = "Step Index"
        
    def connect(self, login, password, server):
        """Connect to MT5 with optimizations"""
        print("Initializing low-latency MT5 connection...")
        
        # Apply system optimizations
        set_process_priority()
        set_cpu_affinity()
        
        # Initialize MT5
        if not mt5.initialize():
            print(f"MT5 initialization failed: {mt5.last_error()}")
            return False
            
        # Login with credentials
        if not mt5.login(login, password=password, server=server):
            print(f"MT5 login failed: {mt5.last_error()}")
            mt5.shutdown()
            return False
            
        print(f"Connected to MT5: {server}")
        print(f"Account: {login}")
        
        # Get account info
        account_info = mt5.account_info()
        if account_info:
            print(f"Balance: ${account_info.balance:.2f}")
            print(f"Equity: ${account_info.equity:.2f}")
            print(f"Leverage: 1:{account_info.leverage}")
            
        self.connected = True
        return True
        
    def get_symbol_info(self):
        """Get Step Index symbol information"""
        # Try different symbol variations
        symbols_to_try = [
            "Step Index",
            "Step Index.0", 
            "STEPINDEX",
            "Step_Index",
            "StepIndex"
        ]
        
        for symbol in symbols_to_try:
            info = mt5.symbol_info(symbol)
            if info:
                print(f"Found symbol: {symbol}")
                print(f"Bid: {info.bid}")
                print(f"Ask: {info.ask}")
                print(f"Spread: {info.spread}")
                print(f"Point: {info.point}")
                print(f"Digits: {info.digits}")
                self.symbol = symbol
                return info
                
        print("Step Index symbol not found. Available symbols:")
        symbols = mt5.symbols_get()
        if symbols:
            step_symbols = [s.name for s in symbols if 'step' in s.name.lower()]
            print(f"Step-related symbols: {step_symbols[:10]}")
        return None
        
    def measure_execution_latency(self, num_tests=10):
        """Measure actual execution latency"""
        print(f"\nMeasuring execution latency ({num_tests} tests)...")
        
        symbol_info = self.get_symbol_info()
        if not symbol_info:
            print("Cannot measure latency - symbol not found")
            return
            
        latencies = []
        
        for i in range(num_tests):
            try:
                # Record start time with high precision
                start_time = time.perf_counter_ns()
                
                # Get current tick (simulates order placement)
                tick = mt5.symbol_info_tick(self.symbol)
                
                # Record end time
                end_time = time.perf_counter_ns()
                
                if tick:
                    latency_ns = end_time - start_time
                    latency_ms = latency_ns / 1_000_000
                    latencies.append(latency_ms)
                    print(f"Test {i+1}: {latency_ms:.3f}ms | Bid: {tick.bid} | Ask: {tick.ask}")
                else:
                    print(f"Test {i+1}: Failed to get tick")
                    
                # Small delay between tests
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Test {i+1}: Error - {e}")
                
        if latencies:
            avg_latency = np.mean(latencies)
            min_latency = np.min(latencies)
            max_latency = np.max(latencies)
            std_latency = np.std(latencies)
            
            print(f"\nLatency Analysis:")
            print(f"Average: {avg_latency:.3f}ms")
            print(f"Minimum: {min_latency:.3f}ms") 
            print(f"Maximum: {max_latency:.3f}ms")
            print(f"Std Dev: {std_latency:.3f}ms")
            print(f"vs Theoretical 922.8ms: {avg_latency/922.8*100:.1f}%")
            
            return avg_latency
        else:
            print("No successful latency measurements")
            return None

    def test_fast_order_execution(self, num_tests=5):
        """Test actual broker execution speed with fast open/close"""
        print(f"Testing actual broker execution speed ({num_tests} round trips)...")

        symbol_info = self.get_symbol_info()
        if not symbol_info:
            return

        execution_times = []

        for test_num in range(num_tests):
            try:
                print(f"\nTest {test_num + 1}/{num_tests}:")

                # Get current price
                tick = mt5.symbol_info_tick(self.symbol)
                if not tick:
                    print("Cannot get current price")
                    continue

                lot_size = 0.20  # Minimum lot size

                # OPEN ORDER - Measure execution time
                open_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": self.symbol,
                    "volume": lot_size,
                    "type": mt5.ORDER_TYPE_BUY,
                    "price": tick.ask,
                    "deviation": 20,
                    "magic": 12345,
                    "comment": f"Fast test {test_num+1}",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_FOK,  # Fill or Kill for speed
                }

                print(f"Opening BUY {lot_size} lots at {tick.ask}")

                # Measure OPEN execution time
                open_start = time.perf_counter_ns()
                open_result = mt5.order_send(open_request)
                open_end = time.perf_counter_ns()

                open_time_ms = (open_end - open_start) / 1_000_000

                if open_result.retcode != mt5.TRADE_RETCODE_DONE:
                    print(f"Open failed: {open_result.retcode} - {open_result.comment}")
                    continue

                print(f"Open executed in: {open_time_ms:.3f}ms | Deal: {open_result.deal}")

                # IMMEDIATELY CLOSE - Measure close execution time
                close_tick = mt5.symbol_info_tick(self.symbol)

                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": self.symbol,
                    "volume": lot_size,
                    "type": mt5.ORDER_TYPE_SELL,
                    "position": open_result.order,
                    "price": close_tick.bid,
                    "deviation": 20,
                    "magic": 12345,
                    "comment": f"Fast close {test_num+1}",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_FOK,
                }

                print(f"Closing at {close_tick.bid}")

                # Measure CLOSE execution time
                close_start = time.perf_counter_ns()
                close_result = mt5.order_send(close_request)
                close_end = time.perf_counter_ns()

                close_time_ms = (close_end - close_start) / 1_000_000

                if close_result.retcode == mt5.TRADE_RETCODE_DONE:
                    print(f"Close executed in: {close_time_ms:.3f}ms | Deal: {close_result.deal}")

                    # Calculate total round-trip time
                    total_time_ms = open_time_ms + close_time_ms
                    execution_times.append({
                        'open_ms': open_time_ms,
                        'close_ms': close_time_ms,
                        'total_ms': total_time_ms
                    })

                    print(f"Total round-trip: {total_time_ms:.3f}ms")

                else:
                    print(f"Close failed: {close_result.retcode} - {close_result.comment}")
                    # Try to close manually if auto-close failed
                    self.emergency_close_all()

                # Small delay between tests
                time.sleep(0.5)

            except Exception as e:
                print(f"Test {test_num+1} error: {e}")
                self.emergency_close_all()

        # Analyze execution times
        if execution_times:
            self.analyze_execution_times(execution_times)
        else:
            print("No successful execution tests completed")

    def emergency_close_all(self):
        """Emergency close all open positions"""
        try:
            positions = mt5.positions_get()
            if positions:
                print(f"Emergency closing {len(positions)} positions...")
                for position in positions:
                    tick = mt5.symbol_info_tick(position.symbol)
                    if position.type == mt5.POSITION_TYPE_BUY:
                        price = tick.bid
                        order_type = mt5.ORDER_TYPE_SELL
                    else:
                        price = tick.ask
                        order_type = mt5.ORDER_TYPE_BUY

                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "symbol": position.symbol,
                        "volume": position.volume,
                        "type": order_type,
                        "position": position.ticket,
                        "price": price,
                        "deviation": 50,
                        "magic": 12345,
                        "comment": "Emergency close",
                        "type_time": mt5.ORDER_TIME_GTC,
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }

                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        print(f"Closed position {position.ticket}")
                    else:
                        print(f"Failed to close {position.ticket}: {result.comment}")
        except Exception as e:
            print(f"Emergency close error: {e}")

    def analyze_execution_times(self, execution_times):
        """Analyze execution time statistics"""
        print(f"\n{'='*60}")
        print("ACTUAL BROKER EXECUTION ANALYSIS")
        print(f"{'='*60}")

        open_times = [t['open_ms'] for t in execution_times]
        close_times = [t['close_ms'] for t in execution_times]
        total_times = [t['total_ms'] for t in execution_times]

        print(f"\nOPEN ORDER EXECUTION:")
        print(f"Average: {np.mean(open_times):.3f}ms")
        print(f"Minimum: {np.min(open_times):.3f}ms")
        print(f"Maximum: {np.max(open_times):.3f}ms")
        print(f"Std Dev: {np.std(open_times):.3f}ms")

        print(f"\nCLOSE ORDER EXECUTION:")
        print(f"Average: {np.mean(close_times):.3f}ms")
        print(f"Minimum: {np.min(close_times):.3f}ms")
        print(f"Maximum: {np.max(close_times):.3f}ms")
        print(f"Std Dev: {np.std(close_times):.3f}ms")

        print(f"\nTOTAL ROUND-TRIP:")
        print(f"Average: {np.mean(total_times):.3f}ms")
        print(f"Minimum: {np.min(total_times):.3f}ms")
        print(f"Maximum: {np.max(total_times):.3f}ms")
        print(f"Std Dev: {np.std(total_times):.3f}ms")

        avg_total = np.mean(total_times)
        print(f"\nCOMPARISON TO BACKTEST:")
        print(f"Actual execution: {avg_total:.3f}ms")
        print(f"Backtest assumption: 922.8ms")
        print(f"Improvement: {(922.8 - avg_total)/922.8*100:.1f}% faster")
        print(f"Speed multiplier: {922.8/avg_total:.1f}x faster")

    def test_two_tick_capture(self, num_tests=10):
        """Test if we can capture 2 ticks (0.2 points) within execution timeframe"""
        print(f"Testing 2-tick capture speed ({num_tests} tests)...")
        print("Monitoring price stability during execution window...")

        symbol_info = self.get_symbol_info()
        if not symbol_info:
            return

        capture_results = []

        for test_num in range(num_tests):
            try:
                print(f"\nCapture Test {test_num + 1}/{num_tests}:")

                # Get initial price
                initial_tick = mt5.symbol_info_tick(self.symbol)
                if not initial_tick:
                    continue

                initial_price = initial_tick.bid
                initial_time = time.perf_counter_ns()

                print(f"Initial price: {initial_price}")

                # Monitor price for ~950ms (average execution time)
                monitoring_duration_ms = 950
                monitoring_duration_ns = monitoring_duration_ms * 1_000_000

                price_changes = []
                max_favorable_move = 0
                max_adverse_move = 0

                while True:
                    current_time = time.perf_counter_ns()
                    elapsed_ns = current_time - initial_time
                    elapsed_ms = elapsed_ns / 1_000_000

                    if elapsed_ms >= monitoring_duration_ms:
                        break

                    # Get current price
                    current_tick = mt5.symbol_info_tick(self.symbol)
                    if current_tick and current_tick.bid != initial_price:
                        price_change = current_tick.bid - initial_price
                        price_changes.append({
                            'time_ms': elapsed_ms,
                            'price': current_tick.bid,
                            'change': price_change
                        })

                        # Track favorable moves (for LONG position)
                        if price_change > max_favorable_move:
                            max_favorable_move = price_change

                        # Track adverse moves
                        if price_change < max_adverse_move:
                            max_adverse_move = price_change

                        print(f"  {elapsed_ms:.1f}ms: {current_tick.bid} ({price_change:+.1f})")

                    # Small delay to avoid overwhelming the API
                    time.sleep(0.001)  # 1ms delay

                # Analyze if 2-tick capture was possible
                two_tick_target = 0.2  # 2 ticks = 0.2 points

                capture_possible = max_favorable_move >= two_tick_target

                result = {
                    'test_num': test_num + 1,
                    'initial_price': initial_price,
                    'max_favorable': max_favorable_move,
                    'max_adverse': max_adverse_move,
                    'price_changes': len(price_changes),
                    'capture_possible': capture_possible,
                    'monitoring_time': monitoring_duration_ms
                }

                capture_results.append(result)

                print(f"  Max favorable move: {max_favorable_move:+.1f} points")
                print(f"  Max adverse move: {max_adverse_move:+.1f} points")
                print(f"  2-tick capture possible: {'YES' if capture_possible else 'NO'}")
                print(f"  Price changes during {monitoring_duration_ms}ms: {len(price_changes)}")

                # Delay between tests
                time.sleep(1.0)

            except Exception as e:
                print(f"Capture test {test_num+1} error: {e}")

        # Analyze capture results
        if capture_results:
            self.analyze_capture_results(capture_results)
        else:
            print("No successful capture tests completed")

    def analyze_capture_results(self, results):
        """Analyze 2-tick capture test results"""
        print(f"\n{'='*60}")
        print("2-TICK CAPTURE ANALYSIS")
        print(f"{'='*60}")

        successful_captures = [r for r in results if r['capture_possible']]
        success_rate = len(successful_captures) / len(results) * 100

        favorable_moves = [r['max_favorable'] for r in results]
        adverse_moves = [r['max_adverse'] for r in results]

        print(f"\nCAPTURE STATISTICS:")
        print(f"Total tests: {len(results)}")
        print(f"Successful 2-tick captures: {len(successful_captures)}")
        print(f"Success rate: {success_rate:.1f}%")

        print(f"\nPRICE MOVEMENT ANALYSIS:")
        print(f"Avg favorable move: {np.mean(favorable_moves):+.3f} points")
        print(f"Max favorable move: {np.max(favorable_moves):+.3f} points")
        print(f"Min favorable move: {np.min(favorable_moves):+.3f} points")

        print(f"Avg adverse move: {np.mean(adverse_moves):+.3f} points")
        print(f"Max adverse move: {np.max(adverse_moves):+.3f} points")
        print(f"Min adverse move: {np.min(adverse_moves):+.3f} points")

        # Calculate if strategy would be viable
        avg_favorable = np.mean(favorable_moves)
        avg_adverse = np.mean(adverse_moves)

        print(f"\nSTRATEGY VIABILITY:")
        print(f"Target: +0.2 points (2 ticks)")
        print(f"Avg favorable move in 950ms: {avg_favorable:+.3f} points")
        print(f"Avg adverse move in 950ms: {avg_adverse:+.3f} points")

        if success_rate >= 70:
            print("✅ 2-tick capture strategy VIABLE")
        elif success_rate >= 50:
            print("⚠️  2-tick capture strategy MARGINAL")
        else:
            print("❌ 2-tick capture strategy NOT VIABLE")

        print(f"\nEXECUTION WINDOW ANALYSIS:")
        print(f"Monitoring duration: 950ms (avg execution time)")
        print(f"Step Index movement: ~0.1 points per second")
        print(f"Expected movement in 950ms: ~0.095 points")
        print(f"Actual avg movement: {avg_favorable:.3f} points")

    def test_order_execution(self):
        """Test actual order execution speed"""
        print("\nTesting order execution speed...")
        
        symbol_info = self.get_symbol_info()
        if not symbol_info:
            return
            
        # Get current price
        tick = mt5.symbol_info_tick(self.symbol)
        if not tick:
            print("Cannot get current price")
            return
            
        print(f"Current Bid: {tick.bid}, Ask: {tick.ask}")
        
        # Test market order (buy)
        lot_size = 0.20  # Minimum lot size
        
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": self.symbol,
            "volume": lot_size,
            "type": mt5.ORDER_TYPE_BUY,
            "price": tick.ask,
            "deviation": 10,
            "magic": 12345,
            "comment": "Low latency test",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,  # Fill or Kill
        }
        
        print(f"Placing test order: BUY {lot_size} lots at {tick.ask}")
        
        # Measure order execution time
        start_time = time.perf_counter_ns()
        result = mt5.order_send(request)
        end_time = time.perf_counter_ns()
        
        execution_time_ms = (end_time - start_time) / 1_000_000
        
        if result.retcode == mt5.TRADE_RETCODE_DONE:
            print(f"Order executed successfully!")
            print(f"Execution time: {execution_time_ms:.3f}ms")
            print(f"Order ticket: {result.order}")
            print(f"Deal ticket: {result.deal}")
            print(f"Executed price: {result.price}")
            
            # Immediately close the position
            self.close_position(result.deal)
            
        else:
            print(f"Order failed: {result.retcode} - {result.comment}")
            
        return execution_time_ms
        
    def close_position(self, deal_ticket):
        """Close position immediately"""
        try:
            # Get position info
            positions = mt5.positions_get(ticket=deal_ticket)
            if not positions:
                print("No position found to close")
                return
                
            position = positions[0]
            tick = mt5.symbol_info_tick(position.symbol)
            
            if position.type == mt5.POSITION_TYPE_BUY:
                price = tick.bid
                order_type = mt5.ORDER_TYPE_SELL
            else:
                price = tick.ask
                order_type = mt5.ORDER_TYPE_BUY
                
            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": order_type,
                "position": position.ticket,
                "price": price,
                "deviation": 10,
                "magic": 12345,
                "comment": "Close test position",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }
            
            start_time = time.perf_counter_ns()
            result = mt5.order_send(close_request)
            end_time = time.perf_counter_ns()
            
            close_time_ms = (end_time - start_time) / 1_000_000
            
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print(f"Position closed successfully in {close_time_ms:.3f}ms")
            else:
                print(f"Close failed: {result.retcode} - {result.comment}")
                
        except Exception as e:
            print(f"Error closing position: {e}")
            
    def disconnect(self):
        """Disconnect from MT5"""
        if self.connected:
            mt5.shutdown()
            print("Disconnected from MT5")
            self.connected = False

def main():
    """Main execution function"""
    print("=" * 60)
    print("LOW-LATENCY MT5 CONNECTION TEST")
    print("=" * 60)
    
    # MT5 credentials
    LOGIN = 5749910
    PASSWORD = "@Ripper25"
    SERVER = "Deriv-Demo"
    
    # Initialize low-latency MT5
    mt5_client = LowLatencyMT5()
    
    try:
        # Connect to MT5
        if not mt5_client.connect(LOGIN, PASSWORD, SERVER):
            print("Failed to connect to MT5")
            return
            
        # Measure latency
        avg_latency = mt5_client.measure_execution_latency(20)
        
        # Test actual order execution
        print("\nTesting ACTUAL order execution latency...")
        mt5_client.test_fast_order_execution()

        # Test 2-tick capture speed
        print("\nTesting 2-tick capture within execution window...")
        mt5_client.test_two_tick_capture()
        
        print(f"\nLatency test completed.")
        if avg_latency:
            print(f"Average latency: {avg_latency:.3f}ms vs theoretical 922.8ms")
            improvement = (922.8 - avg_latency) / 922.8 * 100
            print(f"Improvement: {improvement:.1f}% faster than theoretical")
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        mt5_client.disconnect()

if __name__ == "__main__":
    main()
