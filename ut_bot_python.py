import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Optional

class UTBot:
    """
    UT Bot (Ultimate Trend Bot) Python implementation
    Replicates the Pine Script logic with same parameters and functionality
    """
    
    def __init__(self, key_value: float = 1.0, atr_period: int = 10, use_heikin_ashi: bool = False):
        """
        Initialize UT Bot with parameters matching Pine Script version
        
        Args:
            key_value: Sensitivity factor (default 1.0) - higher = less sensitive
            atr_period: ATR calculation period (default 10)
            use_heikin_ashi: Use Heikin Ashi candles instead of regular price
        """
        self.key_value = key_value
        self.atr_period = atr_period
        self.use_heikin_ashi = use_heikin_ashi
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """Calculate Average True Range"""
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return true_range.rolling(window=self.atr_period).mean()
    
    def heikin_ashi_transform(self, open_price: pd.Series, high: pd.Series, 
                            low: pd.Series, close: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
        """Transform regular OHLC to Heikin Ashi"""
        ha_close = (open_price + high + low + close) / 4
        ha_open = pd.Series(index=close.index, dtype=float)
        ha_high = pd.Series(index=close.index, dtype=float)
        ha_low = pd.Series(index=close.index, dtype=float)
        
        ha_open.iloc[0] = (open_price.iloc[0] + close.iloc[0]) / 2
        
        for i in range(1, len(close)):
            ha_open.iloc[i] = (ha_open.iloc[i-1] + ha_close.iloc[i-1]) / 2
            ha_high.iloc[i] = max(high.iloc[i], ha_open.iloc[i], ha_close.iloc[i])
            ha_low.iloc[i] = min(low.iloc[i], ha_open.iloc[i], ha_close.iloc[i])
        
        # Set first values
        ha_high.iloc[0] = max(high.iloc[0], ha_open.iloc[0], ha_close.iloc[0])
        ha_low.iloc[0] = min(low.iloc[0], ha_open.iloc[0], ha_close.iloc[0])
        
        return ha_open, ha_high, ha_low, ha_close
    
    def calculate_signals(self, open_price: pd.Series, high: pd.Series, 
                         low: pd.Series, close: pd.Series) -> pd.DataFrame:
        """
        Calculate UT Bot signals - main logic replicating Pine Script
        """
        # Use Heikin Ashi if specified
        if self.use_heikin_ashi:
            _, ha_high, ha_low, ha_close = self.heikin_ashi_transform(open_price, high, low, close)
            src = ha_close
            atr_high, atr_low = ha_high, ha_low
        else:
            src = close
            atr_high, atr_low = high, low
        
        # Calculate ATR and loss
        xATR = self.calculate_atr(atr_high, atr_low, src)
        nLoss = self.key_value * xATR
        
        # Initialize arrays
        xATRTrailingStop = pd.Series(index=src.index, dtype=float)
        pos = pd.Series(index=src.index, dtype=int)
        
        # Calculate ATR Trailing Stop (replicating Pine Script logic)
        xATRTrailingStop.iloc[0] = src.iloc[0] - nLoss.iloc[0]
        
        for i in range(1, len(src)):
            prev_stop = xATRTrailingStop.iloc[i-1]
            curr_src = src.iloc[i]
            prev_src = src.iloc[i-1]
            curr_loss = nLoss.iloc[i]
            
            if not pd.isna(prev_stop):
                if curr_src > prev_stop and prev_src > prev_stop:
                    # Uptrend: trailing stop moves up
                    xATRTrailingStop.iloc[i] = max(prev_stop, curr_src - curr_loss)
                elif curr_src < prev_stop and prev_src < prev_stop:
                    # Downtrend: trailing stop moves down
                    xATRTrailingStop.iloc[i] = min(prev_stop, curr_src + curr_loss)
                elif curr_src > prev_stop:
                    # Switch to uptrend
                    xATRTrailingStop.iloc[i] = curr_src - curr_loss
                else:
                    # Switch to downtrend
                    xATRTrailingStop.iloc[i] = curr_src + curr_loss
            else:
                xATRTrailingStop.iloc[i] = curr_src - curr_loss
        
        # Calculate position (replicating Pine Script pos logic)
        pos.iloc[0] = 0
        
        for i in range(1, len(src)):
            prev_stop = xATRTrailingStop.iloc[i-1]
            curr_stop = xATRTrailingStop.iloc[i]
            prev_src = src.iloc[i-1]
            curr_src = src.iloc[i]
            
            if not pd.isna(prev_stop):
                if prev_src < prev_stop and curr_src > curr_stop:
                    pos.iloc[i] = 1  # Long position
                elif prev_src > prev_stop and curr_src < curr_stop:
                    pos.iloc[i] = -1  # Short position
                else:
                    pos.iloc[i] = pos.iloc[i-1]  # Hold previous position
            else:
                pos.iloc[i] = 0
        
        # Calculate EMA and crossovers (replicating Pine Script logic)
        ema = src.ewm(span=1, adjust=False).mean()  # EMA with period 1 = no smoothing
        
        # Crossover logic
        above = (ema > xATRTrailingStop) & (ema.shift(1) <= xATRTrailingStop.shift(1))
        below = (xATRTrailingStop > ema) & (xATRTrailingStop.shift(1) <= ema.shift(1))
        
        # Buy/Sell signals (exact Pine Script logic)
        buy = (src > xATRTrailingStop) & above
        sell = (src < xATRTrailingStop) & below
        
        # Bar colors
        barbuy = src > xATRTrailingStop
        barsell = src < xATRTrailingStop
        
        # Create results DataFrame
        results = pd.DataFrame({
            'close': src,
            'atr': xATR,
            'trailing_stop': xATRTrailingStop,
            'position': pos,
            'ema': ema,
            'buy_signal': buy,
            'sell_signal': sell,
            'bullish_bar': barbuy,
            'bearish_bar': barsell
        })
        
        return results
    
    def plot_signals(self, data: pd.DataFrame, results: pd.DataFrame, 
                    title: str = "UT Bot Signals", figsize: Tuple[int, int] = (15, 10)):
        """
        Plot price data with UT Bot signals
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, height_ratios=[3, 1])
        
        # Main price chart
        ax1.plot(data.index, results['close'], label='Price', linewidth=1, alpha=0.8)
        ax1.plot(data.index, results['trailing_stop'], label='ATR Trailing Stop', 
                linewidth=1, alpha=0.8, color='orange')
        
        # Color bars
        for i in range(len(results)):
            if results['bullish_bar'].iloc[i]:
                ax1.axvspan(data.index[i], data.index[i], alpha=0.1, color='green')
            elif results['bearish_bar'].iloc[i]:
                ax1.axvspan(data.index[i], data.index[i], alpha=0.1, color='red')
        
        # Buy signals
        buy_points = results[results['buy_signal']]
        if not buy_points.empty:
            ax1.scatter(buy_points.index, buy_points['close'], 
                       color='green', marker='^', s=100, label='Buy Signal', zorder=5)
        
        # Sell signals
        sell_points = results[results['sell_signal']]
        if not sell_points.empty:
            ax1.scatter(sell_points.index, sell_points['close'], 
                       color='red', marker='v', s=100, label='Sell Signal', zorder=5)
        
        ax1.set_title(f"{title} - Key Value: {self.key_value}, ATR Period: {self.atr_period}")
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylabel('Price')
        
        # Position subplot
        ax2.fill_between(data.index, 0, results['position'], 
                        where=(results['position'] > 0), color='green', alpha=0.3, label='Long')
        ax2.fill_between(data.index, 0, results['position'], 
                        where=(results['position'] < 0), color='red', alpha=0.3, label='Short')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.set_ylabel('Position')
        ax2.set_xlabel('Time')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def get_current_signal(self, results: pd.DataFrame) -> str:
        """Get the current signal state"""
        if results.empty:
            return "No data"
        
        last_pos = results['position'].iloc[-1]
        last_buy = results['buy_signal'].iloc[-1]
        last_sell = results['sell_signal'].iloc[-1]
        
        if last_buy:
            return "BUY"
        elif last_sell:
            return "SELL"
        elif last_pos == 1:
            return "BULLISH"
        elif last_pos == -1:
            return "BEARISH"
        else:
            return "NEUTRAL"

# Example usage and testing
def create_sample_data(periods: int = 1000) -> pd.DataFrame:
    """Create sample OHLC data for testing"""
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', periods=periods, freq='D')
    
    # Generate realistic price data
    returns = np.random.normal(0.001, 0.02, periods)
    price = 100 * np.exp(np.cumsum(returns))
    
    # Create OHLC from price
    noise = np.random.normal(0, 0.005, periods)
    high = price * (1 + np.abs(noise))
    low = price * (1 - np.abs(noise))
    open_price = np.roll(price, 1)
    open_price[0] = price[0]
    
    return pd.DataFrame({
        'open': open_price,
        'high': high,
        'low': low,
        'close': price
    }, index=dates)

# Example usage
if __name__ == "__main__":
    # Create sample data
    sample_data = create_sample_data(500)
    
    # Initialize UT Bot with default parameters (matching Pine Script)
    ut_bot = UTBot(key_value=1.0, atr_period=10, use_heikin_ashi=False)
    
    # Calculate signals
    results = ut_bot.calculate_signals(
        sample_data['open'], 
        sample_data['high'], 
        sample_data['low'], 
        sample_data['close']
    )
    
    # Display current signal
    current_signal = ut_bot.get_current_signal(results)
    print(f"Current Signal: {current_signal}")
    
    # Show some statistics
    total_buy_signals = results['buy_signal'].sum()
    total_sell_signals = results['sell_signal'].sum()
    print(f"Total Buy Signals: {total_buy_signals}")
    print(f"Total Sell Signals: {total_sell_signals}")
    
    # Plot results (uncomment to see chart)
    # ut_bot.plot_signals(sample_data, results)