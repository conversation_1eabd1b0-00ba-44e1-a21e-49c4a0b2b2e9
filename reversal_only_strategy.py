#!/usr/bin/env python3
"""
REVERSAL-ONLY STRATEGY - Pure Reversal Harvesting
Uses UT Bot signals as triggers but ONLY executes the profitable reversal trades
Skips the initial losing trades and jumps straight to the 100% win rate reversals
"""

import pandas as pd
import numpy as np
from datetime import datetime
import time
from ut_bot_adaptation import UTBot

def reversal_only_strategy():
    """
    Pure reversal strategy - UT Bot signals trigger reversal detection,
    but we only execute the reversal trades (100% win rate)
    """
    print("REVERSAL-ONLY STRATEGY - Pure Reversal Harvesting")
    print("=" * 60)
    
    # Trading constraints
    MIN_VOL = 0.20
    MAX_VOL_PER_POS = 50.0
    MAX_TOTAL_VOL = 200.0
    BRICK_SIZE = 0.1
    COMMISSION_RATE = 0.15
    EXECUTION_DELAY_MS = 922.8
    
    start_time = time.time()
    
    # Load data
    print("Loading Step Index Renko data (3 days)...")
    renko_df = pd.read_csv('step_index_renko_0_1_3days.csv')
    renko_df['datetime'] = pd.to_datetime(renko_df['datetime'])
    print(f"Loaded {len(renko_df)} Renko bricks")
    
    # Calculate time differences for execution delay
    renko_df['time_diff_ms'] = renko_df['datetime'].diff().dt.total_seconds() * 1000
    renko_df['time_diff_ms'].fillna(1000, inplace=True)
    
    # Generate UT Bot signals (for reversal trigger detection)
    print("Generating UT Bot signals for reversal detection...")
    signal_start = time.time()
    ut_bot = UTBot(atr_period=10, sensitivity=1)
    signals_df = ut_bot.run(renko_df)
    signal_time = time.time() - signal_start
    
    buy_count = signals_df['buy'].sum()
    sell_count = signals_df['sell'].sum()
    print(f"Generated {buy_count} BUY and {sell_count} SELL signals in {signal_time:.3f}s")
    
    # Merge data
    data = pd.concat([renko_df, signals_df], axis=1)
    
    # Get signal indices
    buy_signals = data['buy'].fillna(False)
    sell_signals = data['sell'].fillna(False)
    signal_mask = buy_signals | sell_signals
    signal_indices = np.where(signal_mask)[0]
    
    print("Running reversal-only analysis...")
    backtest_start = time.time()
    
    # Initialize tracking
    equity = 10.0
    equity_history = [equity]
    reversal_trades = []
    open_volume = 0.0
    trade_count = 0  # Track number of trades for fixed lot size period
    max_equity = 10.0  # Track peak equity for drawdown calculation
    max_drawdown = 0.0  # Track maximum drawdown

    reversal_stats = {
        'total_reversals': 0,
        'reversal_wins': 0,
        'reversal_losses': 0,
        'reversal_profits': [],
        'reversal_slippage': [],
        'bricks_to_tp': [],
        'reversal_delayed_fills': 0,
        'drawdowns': []  # Track drawdown history
    }
    
    for signal_idx in signal_indices:
        if signal_idx >= len(data) - 30:  # Need room for simulation
            break
            
        # Fixed lot size for first 10 trades, then compounding
        if trade_count < 10:
            # First 10 trades use fixed minimum lot size
            lot_size = MIN_VOL
            available_vol = MAX_TOTAL_VOL - open_volume
            if available_vol < MIN_VOL:
                continue
        else:
            # After 10 trades, use compounding risk calculation
            risk_percentage = 0.04
            risk_amount = equity * risk_percentage
            price_risk = 2 * BRICK_SIZE
            ideal_lot_size = risk_amount / (price_risk * 10)

            # Apply volume constraints
            available_vol = MAX_TOTAL_VOL - open_volume
            lot_size = max(MIN_VOL, min(ideal_lot_size, MAX_VOL_PER_POS, available_vol))

            if lot_size < MIN_VOL or available_vol < MIN_VOL:
                continue
            
        # Signal details
        signal_time_dt = data['datetime'].iloc[signal_idx]
        signal_price = data['close'].iloc[signal_idx]
        initial_position_type = "LONG" if buy_signals.iloc[signal_idx] else "SHORT"
        
        # SIMULATE initial trade to find reversal trigger point
        reversal_trigger_idx = simulate_initial_trade_for_reversal(
            signal_idx, data, initial_position_type, signal_price
        )
        
        if reversal_trigger_idx is None:
            continue  # No reversal trigger found
            
        # EXECUTE ONLY THE REVERSAL TRADE
        reversal_position_type = "SHORT" if initial_position_type == "LONG" else "LONG"
        reversal_signal_price = data['close'].iloc[reversal_trigger_idx]
        reversal_signal_time = data['datetime'].iloc[reversal_trigger_idx]
        
        # Apply execution delay to reversal entry
        reversal_execution_price, reversal_execution_idx, reversal_fill_type = calculate_execution_price_vectorized(
            reversal_trigger_idx, data, EXECUTION_DELAY_MS, reversal_signal_price
        )
        
        # Calculate reversal slippage
        reversal_slippage = reversal_execution_price - reversal_signal_price if reversal_position_type == "LONG" else reversal_signal_price - reversal_execution_price
        
        # Update open volume
        open_volume += lot_size
        
        # Execute reversal trade with 2-brick TP (no SL - 100% win rate)
        reversal_profit, reversal_exit_price, reversal_exit_time, reversal_outcome, bricks_to_tp = execute_reversal_trade(
            reversal_execution_idx, data, reversal_position_type, reversal_execution_price,
            lot_size, BRICK_SIZE, COMMISSION_RATE
        )
        
        # Update equity
        equity += reversal_profit
        equity_history.append(equity)

        # Calculate drawdown
        if equity > max_equity:
            max_equity = equity

        current_drawdown = (max_equity - equity) / max_equity * 100
        if current_drawdown > max_drawdown:
            max_drawdown = current_drawdown

        reversal_stats['drawdowns'].append(current_drawdown)

        # Update open volume
        open_volume -= lot_size
        open_volume = max(0, open_volume)

        # Increment trade counter
        trade_count += 1
        
        # Track reversal statistics
        reversal_stats['total_reversals'] += 1
        reversal_stats['reversal_profits'].append(reversal_profit)
        reversal_stats['reversal_slippage'].append(reversal_slippage)
        reversal_stats['bricks_to_tp'].append(bricks_to_tp)
        reversal_stats['reversal_delayed_fills'] += 1
        
        if reversal_profit > 0:
            reversal_stats['reversal_wins'] += 1
        else:
            reversal_stats['reversal_losses'] += 1
        
        # Record reversal trade
        reversal_trades.append({
            'trade_number': trade_count,
            'signal_time': signal_time_dt,
            'initial_signal_type': initial_position_type,
            'reversal_signal_time': reversal_signal_time,
            'reversal_signal_price': reversal_signal_price,
            'reversal_execution_price': reversal_execution_price,
            'reversal_slippage': round(reversal_slippage, 5),
            'reversal_fill_type': reversal_fill_type,
            'reversal_position_type': reversal_position_type,
            'volume': round(lot_size, 2),
            'is_fixed_lot': trade_count <= 10,
            'reversal_exit_time': reversal_exit_time,
            'reversal_exit_price': reversal_exit_price,
            'reversal_outcome': reversal_outcome,
            'reversal_profit': round(reversal_profit, 2),
            'bricks_to_tp': bricks_to_tp,
            'balance': round(equity, 2),
            'drawdown': round(current_drawdown, 2)
        })
        
        # Safety check
        if equity < 0:
            print(f"Balance went negative: ${equity:.2f}")
            break
    
    backtest_time = time.time() - backtest_start
    total_time = time.time() - start_time
    
    print(f"Reversal analysis completed in {backtest_time:.3f}s")
    print(f"Total execution time: {total_time:.3f}s")
    
    # Print results
    print_reversal_only_results(reversal_trades, equity, reversal_stats, max_drawdown)

def simulate_initial_trade_for_reversal(signal_idx, data, position_type, entry_price):
    """
    Simulate initial trade to find where SL would be hit (reversal trigger point)
    Returns the index where reversal should be triggered, or None if no trigger
    """
    end_idx = min(signal_idx + 20, len(data))
    
    if signal_idx + 1 >= end_idx:
        return None
    
    sl_bricks = 2  # 2 brick SL for initial trade
    
    for i in range(signal_idx + 1, end_idx):
        direction = data['direction'].iloc[i]
        
        if position_type == "LONG":
            if direction == 'down':
                sl_bricks -= 1
                if sl_bricks <= 0:
                    return i  # SL hit - reversal trigger point
        else:  # SHORT
            if direction == 'up':
                sl_bricks -= 1
                if sl_bricks <= 0:
                    return i  # SL hit - reversal trigger point
    
    return None  # No SL hit within window

def calculate_execution_price_vectorized(signal_idx, data, delay_ms, signal_price):
    """
    Calculate realistic execution price after delay (same as before)
    """
    if signal_idx + 1 >= len(data):
        return signal_price, signal_idx, 'instant_fills'
    
    time_diffs = data['time_diff_ms'].iloc[signal_idx+1:signal_idx+10].values
    if len(time_diffs) == 0:
        return signal_price, signal_idx, 'instant_fills'
    
    cumulative_time = np.cumsum(time_diffs)
    delay_reached_idx = np.where(cumulative_time >= delay_ms)[0]
    
    if len(delay_reached_idx) == 0:
        execution_idx = min(signal_idx + len(time_diffs), len(data) - 1)
        execution_price = data['close'].iloc[execution_idx]
        return execution_price, execution_idx, 'delayed_fills'
    
    execution_offset = delay_reached_idx[0] + 1
    execution_idx = signal_idx + execution_offset
    
    if execution_idx >= len(data):
        execution_idx = len(data) - 1
    
    execution_price = data['close'].iloc[execution_idx]
    
    if execution_price == signal_price:
        return execution_price, execution_idx, 'instant_fills'
    else:
        return execution_price, execution_idx, 'delayed_fills'

def execute_reversal_trade(execution_idx, data, position_type, execution_price, lot_size, BRICK_SIZE, COMMISSION_RATE):
    """
    Execute reversal trade with 2-brick TP (no SL for 100% win rate)
    """
    end_idx = len(data)
    
    if execution_idx + 1 >= end_idx:
        return 0, execution_price, data['datetime'].iloc[execution_idx], 'NO_DATA', 0
    
    tp_bricks = 2  # 2 brick TP target
    bricks_to_tp = 0
    
    for i in range(execution_idx + 1, end_idx):
        direction = data['direction'].iloc[i]
        current_price = data['close'].iloc[i]
        current_time = data['datetime'].iloc[i]
        bricks_to_tp += 1
        
        if position_type == "LONG":
            if direction == 'up':
                tp_bricks -= 1
                if tp_bricks <= 0:
                    # TP condition met - apply 922.8ms exit delay
                    exit_signal_price = current_price
                    exit_signal_time = current_time
                    exit_signal_idx = i

                    # Calculate actual exit price with execution delay
                    actual_exit_price, actual_exit_idx, exit_fill_type = calculate_execution_price_vectorized(
                        exit_signal_idx, data, 922.8, exit_signal_price
                    )

                    # Use actual exit price for profit calculation
                    gross_profit = (actual_exit_price - execution_price) * 10 * lot_size
                    commission = gross_profit * COMMISSION_RATE if gross_profit > 0 else 0
                    profit = gross_profit - commission

                    # Update bricks count to include exit delay
                    exit_bricks_added = actual_exit_idx - exit_signal_idx
                    total_bricks_to_tp = bricks_to_tp + exit_bricks_added

                    actual_exit_time = data['datetime'].iloc[actual_exit_idx] if actual_exit_idx < len(data) else current_time
                    return profit, actual_exit_price, actual_exit_time, 'LONG_TP_REVERSAL', total_bricks_to_tp
        else:  # SHORT
            if direction == 'down':
                tp_bricks -= 1
                if tp_bricks <= 0:
                    # TP condition met - apply 922.8ms exit delay
                    exit_signal_price = current_price
                    exit_signal_time = current_time
                    exit_signal_idx = i

                    # Calculate actual exit price with execution delay
                    actual_exit_price, actual_exit_idx, exit_fill_type = calculate_execution_price_vectorized(
                        exit_signal_idx, data, 922.8, exit_signal_price
                    )

                    # Use actual exit price for profit calculation
                    gross_profit = (execution_price - actual_exit_price) * 10 * lot_size
                    commission = gross_profit * COMMISSION_RATE if gross_profit > 0 else 0
                    profit = gross_profit - commission

                    # Update bricks count to include exit delay
                    exit_bricks_added = actual_exit_idx - exit_signal_idx
                    total_bricks_to_tp = bricks_to_tp + exit_bricks_added

                    actual_exit_time = data['datetime'].iloc[actual_exit_idx] if actual_exit_idx < len(data) else current_time
                    return profit, actual_exit_price, actual_exit_time, 'SHORT_TP_REVERSAL', total_bricks_to_tp
    
    # Time exit (shouldn't happen with 100% win rate, but safety)
    exit_price = data['close'].iloc[end_idx-1]
    exit_time = data['datetime'].iloc[end_idx-1]
    
    if position_type == "LONG":
        gross_profit = (exit_price - execution_price) * 10 * lot_size
    else:
        gross_profit = (execution_price - exit_price) * 10 * lot_size
    
    if gross_profit > 0:
        commission = gross_profit * COMMISSION_RATE
        profit = gross_profit - commission
    else:
        profit = gross_profit
    
    return profit, exit_price, exit_time, 'TIME_EXIT_REVERSAL', bricks_to_tp

def print_reversal_only_results(trades, final_equity, reversal_stats, max_drawdown):
    """Print reversal-only strategy results with drawdown analysis"""
    print("\n" + "=" * 60)
    print("REVERSAL-ONLY STRATEGY RESULTS")
    print("=" * 60)

    initial_equity = 10.0
    trades_df = pd.DataFrame(trades)

    print(f"Initial Balance:     ${initial_equity:.2f}")
    print(f"Final Balance:       ${final_equity:.2f}")
    print(f"Total Return:        ${final_equity - initial_equity:+.2f}")
    print(f"Return %:            {((final_equity / initial_equity) - 1) * 100:+.2f}%")
    print(f"Maximum Drawdown:    {max_drawdown:.2f}%")
    
    if len(trades_df) > 0:
        winning_trades = trades_df[trades_df['reversal_profit'] > 0]
        losing_trades = trades_df[trades_df['reversal_profit'] <= 0]
        
        print(f"\nREVERSAL TRADE STATISTICS:")
        print(f"Total Reversal Trades: {len(trades_df)}")
        print(f"Winning Reversals:     {len(winning_trades)} ({len(winning_trades)/len(trades_df)*100:.1f}%)")
        print(f"Losing Reversals:      {len(losing_trades)} ({len(losing_trades)/len(trades_df)*100:.1f}%)")
        
        if len(winning_trades) > 0:
            print(f"Avg Reversal Win:      ${winning_trades['reversal_profit'].mean():.2f}")
            print(f"Max Reversal Win:      ${winning_trades['reversal_profit'].max():.2f}")
        
        if len(losing_trades) > 0:
            print(f"Avg Reversal Loss:     ${losing_trades['reversal_profit'].mean():.2f}")
            print(f"Max Reversal Loss:     ${losing_trades['reversal_profit'].min():.2f}")

        # Fixed lot size vs compounding analysis
        fixed_lot_trades = trades_df[trades_df['is_fixed_lot'] == True]
        compounding_trades = trades_df[trades_df['is_fixed_lot'] == False]

        print(f"\nLOT SIZE ANALYSIS:")
        print(f"Fixed lot trades (first 10): {len(fixed_lot_trades)}")
        if len(fixed_lot_trades) > 0:
            print(f"Fixed lot profit:        ${fixed_lot_trades['reversal_profit'].sum():.2f}")
            print(f"Avg fixed lot profit:    ${fixed_lot_trades['reversal_profit'].mean():.2f}")

        print(f"Compounding trades:      {len(compounding_trades)}")
        if len(compounding_trades) > 0:
            print(f"Compounding profit:      ${compounding_trades['reversal_profit'].sum():.2f}")
            print(f"Avg compounding profit:  ${compounding_trades['reversal_profit'].mean():.2f}")
            print(f"Min compounding volume:  {compounding_trades['volume'].min():.2f}")
            print(f"Max compounding volume:  {compounding_trades['volume'].max():.2f}")

        # Drawdown analysis
        if reversal_stats['drawdowns']:
            avg_drawdown = np.mean(reversal_stats['drawdowns'])
            print(f"\nDRAWDOWN ANALYSIS:")
            print(f"Average drawdown:        {avg_drawdown:.2f}%")
            print(f"Maximum drawdown:        {max_drawdown:.2f}%")
            print(f"Drawdown periods:        {sum(1 for dd in reversal_stats['drawdowns'] if dd > 0)}")
        
        # Reversal timing analysis
        if reversal_stats['bricks_to_tp']:
            avg_bricks = np.mean(reversal_stats['bricks_to_tp'])
            max_bricks = max(reversal_stats['bricks_to_tp'])
            min_bricks = min(reversal_stats['bricks_to_tp'])
            print(f"\nREVERSAL TIMING ANALYSIS:")
            print(f"Avg bricks to hit 2-brick TP: {avg_bricks:.1f}")
            print(f"Max bricks to hit TP: {max_bricks}")
            print(f"Min bricks to hit TP: {min_bricks}")
            print(f"Avg time to hit TP: ~{avg_bricks:.1f} seconds")
            print(f"Max time to hit TP: ~{max_bricks:.1f} seconds")
        
        # Performance metrics
        if len(losing_trades) > 0:
            profit_factor = winning_trades['reversal_profit'].sum() / abs(losing_trades['reversal_profit'].sum())
            print(f"\nProfit Factor:       {profit_factor:.2f}")
        else:
            print(f"\nProfit Factor:       ∞ (no losses)")
        
        # Show sample reversal trades
        print(f"\nSAMPLE REVERSAL TRADES:")
        for _, trade in trades_df.head(10).iterrows():
            lot_type = "FIXED" if trade['is_fixed_lot'] else "COMP"
            print(f"  #{trade['trade_number']:2d} {lot_type} | {trade['initial_signal_type']} SL → {trade['reversal_position_type']} | "
                  f"Vol: {trade['volume']:.2f} | ${trade['reversal_profit']:+7.2f} | "
                  f"{trade['bricks_to_tp']} bricks | DD: {trade['drawdown']:.1f}%")
    
    print("=" * 60)

if __name__ == "__main__":
    reversal_only_strategy()
