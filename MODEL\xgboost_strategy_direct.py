import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import xgboost as xgb
from datetime import datetime
import os
import pickle
import math

# Set up logging
log_dir = "strategy_logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"{log_dir}/xgboost_strategy_{timestamp}.log"

# Function to log messages
def log_message(message):
    """Log message to file and print to console"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp}] {message}"
    print(log_msg)
    with open(log_filename, 'a') as f:
        f.write(log_msg + '\n')

log_message("=== XGBOOST TRADING STRATEGY (DIRECT) ===")

# VECTORIZED FEATURE ENGINEERING
def calculate_vectorized_features(df):
    """Calculate ALL features using vectorized operations for sub-1ms performance"""
    import time
    start_time = time.perf_counter()

    log_message("Starting vectorized feature engineering...")

    # Convert to numpy arrays for maximum speed
    close_prices = df['close'].values
    directions = (df['direction'] == 'up').astype(int).values  # 1 for up, 0 for down
    datetimes = pd.to_datetime(df['datetime'])

    n = len(close_prices)

    # Pre-allocate feature arrays
    features = {}

    # Basic features (vectorized)
    features['price'] = close_prices
    features['price_change'] = np.concatenate([[0], np.diff(close_prices)])
    features['price_pct_change'] = np.concatenate([[0], np.diff(close_prices) / close_prices[:-1]])
    features['direction'] = directions * 2 - 1  # Convert to -1/1

    # Time features (vectorized)
    features['hour'] = datetimes.dt.hour.values
    features['minute'] = datetimes.dt.minute.values
    features['day_of_week'] = datetimes.dt.dayofweek.values

    # Vectorized streak calculations
    up_streaks = np.zeros(n)
    down_streaks = np.zeros(n)

    # Calculate streaks using vectorized operations
    direction_changes = np.concatenate([[True], directions[1:] != directions[:-1]])
    streak_starts = np.where(direction_changes)[0]

    for i in range(len(streak_starts)):
        start_idx = streak_starts[i]
        end_idx = streak_starts[i + 1] if i + 1 < len(streak_starts) else n

        if directions[start_idx] == 1:  # Up streak
            up_streaks[start_idx:end_idx] = np.arange(1, end_idx - start_idx + 1)
        else:  # Down streak
            down_streaks[start_idx:end_idx] = np.arange(1, end_idx - start_idx + 1)

    features['up_streak'] = up_streaks
    features['down_streak'] = down_streaks

    # Vectorized rolling window calculations for multiple periods
    periods = [5, 10, 15, 20, 30]

    for period in periods:
        # Price momentum (vectorized rolling)
        price_momentum = np.full(n, np.nan)
        price_momentum[period-1:] = close_prices[period-1:] - close_prices[:-period+1]
        features[f'price_momentum_{period}'] = np.nan_to_num(price_momentum)

        # Price percentage change (vectorized rolling)
        price_pct_change_period = np.full(n, np.nan)
        price_pct_change_period[period-1:] = (close_prices[period-1:] - close_prices[:-period+1]) / close_prices[:-period+1]
        features[f'price_pct_change_{period}'] = np.nan_to_num(price_pct_change_period)

        # Rolling up/down counts (vectorized)
        up_count = np.full(n, np.nan)
        down_count = np.full(n, np.nan)

        # Use pandas rolling for efficient window operations
        up_series = pd.Series(directions)
        down_series = pd.Series(1 - directions)

        up_count[period-1:] = up_series.rolling(window=period).sum().values[period-1:]
        down_count[period-1:] = down_series.rolling(window=period).sum().values[period-1:]

        features[f'up_count_{period}'] = np.nan_to_num(up_count)
        features[f'down_count_{period}'] = np.nan_to_num(down_count)

        # Rolling volatility (vectorized)
        volatility = np.full(n, np.nan)
        price_series = pd.Series(close_prices)
        price_changes = price_series.diff()
        volatility[period-1:] = price_changes.rolling(window=period).std().values[period-1:]
        features[f'volatility_{period}'] = np.nan_to_num(volatility)

        # Max streaks in rolling windows (optimized)
        max_up_streak = np.full(n, np.nan)
        max_down_streak = np.full(n, np.nan)

        for i in range(period-1, n):
            window_directions = directions[i-period+1:i+1]

            # Calculate max streaks in window
            current_up = 0
            current_down = 0
            max_up = 0
            max_down = 0

            for d in window_directions:
                if d == 1:
                    current_up += 1
                    current_down = 0
                    max_up = max(max_up, current_up)
                else:
                    current_down += 1
                    current_up = 0
                    max_down = max(max_down, current_down)

            max_up_streak[i] = max_up
            max_down_streak[i] = max_down

        features[f'max_up_streak_{period}'] = np.nan_to_num(max_up_streak)
        features[f'max_down_streak_{period}'] = np.nan_to_num(max_down_streak)

    # Convert to DataFrame
    feature_df = pd.DataFrame(features)
    feature_df['datetime'] = df['datetime'].values

    end_time = time.perf_counter()
    execution_time = (end_time - start_time) * 1000  # Convert to milliseconds

    log_message(f"Vectorized feature engineering completed in {execution_time:.2f}ms")
    log_message(f"Generated {len(features)} features for {n} data points")

    return feature_df

# VECTORIZED EXECUTION DELAY SIMULATION
def calculate_vectorized_execution_price(signal_idx, data, delay_ms, signal_price):
    """Calculate realistic execution price with vectorized 922.8ms delay simulation"""
    import time
    start_time = time.perf_counter()

    # Convert delay to number of ticks (assuming ~100ms per tick average)
    delay_ticks = max(1, int(delay_ms / 100))

    # Calculate execution index with delay
    execution_idx = min(signal_idx + delay_ticks, len(data) - 1)

    # Get execution price (vectorized lookup)
    execution_price = data.iloc[execution_idx]['close']

    end_time = time.perf_counter()
    execution_time = (end_time - start_time) * 1000

    return execution_price, execution_idx

# Load the FULL 3-day Renko data for backtesting
log_message("Loading FULL 3-day Step Index Renko data...")
renko_df = pd.read_csv('C:/Users/<USER>/Documents/QT-META/step_index_renko_0_1_3days.csv')
renko_df['datetime'] = pd.to_datetime(renko_df['datetime'])

# Load the trained model
model_file = "xgboost_model_20250521_145606.pkl"
log_message(f"Loading model from {model_file}...")
with open(model_file, 'rb') as file:
    model = pickle.load(file)

# Generate vectorized features from Renko data
log_message("Generating vectorized features from Renko data...")
vectorized_features = calculate_vectorized_features(renko_df)
log_message(f"Vectorized features shape: {vectorized_features.shape}")

# Functions already defined above - removing duplicates

# Dynamic risk management function
def calculate_dynamic_risk(equity, equity_history, win_streak, loss_streak, recent_outcomes=None):
    """Calculate dynamic risk percentage based on multiple factors"""
    base_risk = 0.04  # Base risk percentage

    # 1. Streak-based adjustment
    if win_streak >= 5:
        streak_factor = 1.3
    elif win_streak >= 3:
        streak_factor = 1.15
    elif loss_streak >= 3:
        streak_factor = 0.7
    elif loss_streak >= 1:
        streak_factor = 0.85
    else:
        streak_factor = 1.0

    # 2. Equity milestone adjustment
    if equity < 1000:
        equity_factor = 1.0
    elif equity < 10000:
        equity_factor = 0.95
    elif equity < 100000:
        equity_factor = 0.9
    elif equity < 1000000:
        equity_factor = 0.85
    else:
        equity_factor = 0.8

    # 3. Recent performance adjustment
    if recent_outcomes and len(recent_outcomes) >= 20:
        win_rate = sum(1 for t in recent_outcomes[-20:] if t > 0) / 20
        if win_rate > 0.9:
            performance_factor = 1.2
        elif win_rate > 0.8:
            performance_factor = 1.1
        elif win_rate < 0.5:
            performance_factor = 0.8
        else:
            performance_factor = 1.0
    else:
        performance_factor = 1.0

    # 4. Drawdown protection
    if len(equity_history) > 1:
        peak = max(equity_history)
        drawdown = (peak - equity) / peak
        if drawdown > 0.2:
            drawdown_factor = 0.6
        elif drawdown > 0.1:
            drawdown_factor = 0.8
        else:
            drawdown_factor = 1.0
    else:
        drawdown_factor = 1.0

    # Calculate final risk percentage
    risk = base_risk * streak_factor * equity_factor * performance_factor * drawdown_factor

    # Ensure risk stays within reasonable bounds
    return max(0.01, min(risk, 0.06))  # Cap between 1% and 6%

# Position sizing function
def calculate_position_size(risk_amount, price_risk, max_per_position, max_total_vol, current_open_vol):
    """Calculate optimal position allocation respecting volume constraints"""
    # Calculate ideal lot size
    ideal_lot_size = risk_amount / (price_risk * 10)

    # Available volume within total limit
    available_vol = max_total_vol - current_open_vol

    # Check if we need multiple positions
    if ideal_lot_size <= max_per_position:
        # Single position is sufficient
        lot_size = min(ideal_lot_size, max_per_position, available_vol)
        num_positions = 1 if lot_size > 0 else 0
    else:
        # Need multiple positions
        max_possible_vol = min(ideal_lot_size, available_vol)
        if max_possible_vol <= 0:
            return 0, 0

        num_positions = math.ceil(max_possible_vol / max_per_position)
        lot_size = min(max_per_position, max_possible_vol / num_positions)

    return num_positions, lot_size

# Extract features for prediction in the EXACT order the model expects
expected_feature_order = [
    'price', 'price_change', 'price_pct_change', 'direction', 'up_streak', 'down_streak',
    'price_momentum_5', 'price_pct_change_5', 'up_count_5', 'down_count_5', 'volatility_5', 'max_up_streak_5', 'max_down_streak_5',
    'price_momentum_10', 'price_pct_change_10', 'up_count_10', 'down_count_10', 'volatility_10', 'max_up_streak_10', 'max_down_streak_10',
    'price_momentum_15', 'price_pct_change_15', 'up_count_15', 'down_count_15', 'volatility_15', 'max_up_streak_15', 'max_down_streak_15',
    'price_momentum_20', 'price_pct_change_20', 'up_count_20', 'down_count_20', 'volatility_20', 'max_up_streak_20', 'max_down_streak_20',
    'price_momentum_30', 'price_pct_change_30', 'up_count_30', 'down_count_30', 'volatility_30', 'max_up_streak_30', 'max_down_streak_30',
    'hour', 'minute', 'day_of_week'
]

# Reorder features to match model expectations
X = vectorized_features[expected_feature_order]

# Make predictions on the vectorized features
log_message("Making predictions on vectorized features...")
predictions = model.predict(X)

# Map predictions back to original labels (-1, 0, 1)
predictions_original = np.array([0, -1, 1])[predictions.astype(int)]

# Get probabilities
probabilities = model.predict_proba(X)

# Create a DataFrame with predictions
result_df = pd.DataFrame({
    'datetime': vectorized_features['datetime'],
    'prediction': predictions_original,
    'prob_short': probabilities[:, 0],  # Class 0 = Short (-1)
    'prob_neutral': probabilities[:, 1],  # Class 1 = Neutral (0)
    'prob_long': probabilities[:, 2]  # Class 2 = Long (1)
})

# Merge with Renko data
log_message("Merging predictions with Renko data...")
renko_with_predictions = pd.merge_asof(
    renko_df.sort_values('datetime'),
    result_df.sort_values('datetime'),
    on='datetime',
    direction='nearest'
)

# Fill NaN values in prediction columns
renko_with_predictions['prediction'] = renko_with_predictions['prediction'].fillna(0)
renko_with_predictions['prob_short'] = renko_with_predictions['prob_short'].fillna(0)
renko_with_predictions['prob_neutral'] = renko_with_predictions['prob_neutral'].fillna(1)
renko_with_predictions['prob_long'] = renko_with_predictions['prob_long'].fillna(0)

# Save predictions
renko_with_predictions.to_csv(f"{log_dir}/predictions_{timestamp}.csv", index=False)
log_message(f"Saved predictions to {log_dir}/predictions_{timestamp}.csv")

# Backtest the strategy
log_message("Backtesting the strategy...")

# Strategy parameters
BRICK_SIZE = 0.05
SPREAD = 0.0
COMMISSION_RATE = 0.15  # 15% commission on profits for zero spread account
MIN_VOL = 0.10
MAX_VOL_PER_POS = 50.0
MAX_TOTAL_VOL = 200.0
CONFIDENCE_THRESHOLD = 0.6  # Minimum probability to enter a trade

def apply_commission(profit):
    """Apply 15% commission on profits only. Losses are not charged commission."""
    if profit > 0:
        commission = profit * COMMISSION_RATE
        net_profit = profit - commission
        return net_profit, commission
    else:
        return profit, 0.0  # No commission on losses

# Account state
equity = 10.0  # Starting with $10 as requested
open_volume = 0
equity_history = [equity]
win_streak = 0
loss_streak = 0
trade_outcomes = []
win_count = 0
trade_count = 0
total_commission = 0.0  # Track total commission paid

# Trading results
trades = []

# Backtest loop
i = 30  # Start after enough data for features
while i < len(renko_with_predictions) - 10:  # Leave some room at the end for trade management
    current_row = renko_with_predictions.iloc[i]

    # Check if we have a prediction with high confidence
    if (current_row['prediction'] == 1 and current_row['prob_long'] >= CONFIDENCE_THRESHOLD) or \
       (current_row['prediction'] == -1 and current_row['prob_short'] >= CONFIDENCE_THRESHOLD):

        # Calculate dynamic risk
        risk_percentage = calculate_dynamic_risk(
            equity,
            equity_history,
            win_streak,
            loss_streak,
            trade_outcomes
        )

        # Calculate risk amount and price risk
        risk_amount = equity * risk_percentage
        price_risk = 0.2 + SPREAD  # 2 bricks + spread

        # Calculate position size
        num_positions, lot_size = calculate_position_size(
            risk_amount,
            price_risk,
            MAX_VOL_PER_POS,
            MAX_TOTAL_VOL,
            open_volume
        )

        if num_positions > 0 and lot_size >= MIN_VOL:
            # VECTORIZED EXECUTION WITH 922.8ms DELAY
            signal_price = current_row['close']
            execution_price, execution_idx = calculate_vectorized_execution_price(
                i, renko_with_predictions, 922.8, signal_price
            )

            # Execute trade with realistic execution price
            entry_time = current_row['datetime']
            entry_price = execution_price  # Use execution price with delay
            position_type = "LONG" if current_row['prediction'] == 1 else "SHORT"
            position_lot_size = lot_size
            open_volume += position_lot_size
            trade_count += 1

            log_message(f"Trade #{trade_count} executed - {position_type} at {entry_time}")
            log_message(f"Signal price: {signal_price:.5f}, Execution price: {execution_price:.5f} (922.8ms delay)")
            log_message(f"Volume: {position_lot_size:.2f}, Slippage: {abs(execution_price - signal_price):.5f}")

            # Trade management
            tp_bricks = 5  # Take profit at 5 bricks
            sl_bricks = 2  # Stop loss at 2 bricks

            # Initialize trade outcome variables
            profit = 0
            trade_commission = 0.0
            outcome = None
            exit_price = entry_price
            exit_time = entry_time

            # Simulate trade
            for j in range(i + 1, min(i + 20, len(renko_with_predictions))):
                move = renko_with_predictions.iloc[j]['direction']
                current_price = renko_with_predictions.iloc[j]['close']

                if position_type == "LONG":
                    if move == 'up':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            gross_profit = (0.5 - SPREAD) * 10 * position_lot_size
                            profit, trade_commission = apply_commission(gross_profit)
                            outcome = 'LONG_TP'
                            exit_price = current_price
                            exit_time = renko_with_predictions.iloc[j]['datetime']
                            log_message(f"LONG_TP hit. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                            break
                    elif move == 'down':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            outcome = 'LONG_SL'
                            log_message("LONG_SL hit, preparing for short reversal")

                            # Short reversal
                            tp_bricks = 2  # Take profit at 2 bricks down
                            sl_bricks = 5  # Stop loss at 5 bricks up

                            for k in range(j + 1, min(j + 20, len(renko_with_predictions))):
                                move = renko_with_predictions.iloc[k]['direction']

                                if move == 'down':
                                    tp_bricks -= 1
                                    if tp_bricks == 0:
                                        gross_profit = (0.2 - SPREAD) * 10 * position_lot_size
                                        profit, trade_commission = apply_commission(gross_profit)
                                        outcome = 'SHORT_TP_AFTER_LONG_SL'
                                        exit_price = renko_with_predictions.iloc[k]['close']
                                        exit_time = renko_with_predictions.iloc[k]['datetime']
                                        log_message(f"SHORT_TP hit after LONG_SL. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                                        break
                                elif move == 'up':
                                    sl_bricks -= 1
                                    if sl_bricks == 0:
                                        profit = -(0.5 + SPREAD) * 10 * position_lot_size
                                        outcome = 'SHORT_SL_AFTER_LONG_SL'
                                        exit_price = renko_with_predictions.iloc[k]['close']
                                        exit_time = renko_with_predictions.iloc[k]['datetime']
                                        log_message(f"SHORT_SL hit after LONG_SL. Loss: ${profit:.2f}")
                                        break

                            if outcome == 'LONG_SL':  # If no exit in the short reversal
                                profit = -(0.2 + SPREAD) * 10 * position_lot_size
                                exit_price = renko_with_predictions.iloc[j]['close']
                                exit_time = renko_with_predictions.iloc[j]['datetime']
                            break

                elif position_type == "SHORT":
                    if move == 'down':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            gross_profit = (0.5 - SPREAD) * 10 * position_lot_size
                            profit, trade_commission = apply_commission(gross_profit)
                            outcome = 'SHORT_TP'
                            exit_price = current_price
                            exit_time = renko_with_predictions.iloc[j]['datetime']
                            log_message(f"SHORT_TP hit. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                            break
                    elif move == 'up':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            outcome = 'SHORT_SL'
                            log_message("SHORT_SL hit, preparing for long reversal")

                            # Long reversal
                            tp_bricks = 2  # Take profit at 2 bricks up
                            sl_bricks = 5  # Stop loss at 5 bricks down

                            for k in range(j + 1, min(j + 20, len(renko_with_predictions))):
                                move = renko_with_predictions.iloc[k]['direction']

                                if move == 'up':
                                    tp_bricks -= 1
                                    if tp_bricks == 0:
                                        gross_profit = (0.2 - SPREAD) * 10 * position_lot_size
                                        profit, trade_commission = apply_commission(gross_profit)
                                        outcome = 'LONG_TP_AFTER_SHORT_SL'
                                        exit_price = renko_with_predictions.iloc[k]['close']
                                        exit_time = renko_with_predictions.iloc[k]['datetime']
                                        log_message(f"LONG_TP hit after SHORT_SL. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                                        break
                                elif move == 'down':
                                    sl_bricks -= 1
                                    if sl_bricks == 0:
                                        profit = -(0.5 + SPREAD) * 10 * position_lot_size
                                        outcome = 'LONG_SL_AFTER_SHORT_SL'
                                        exit_price = renko_with_predictions.iloc[k]['close']
                                        exit_time = renko_with_predictions.iloc[k]['datetime']
                                        log_message(f"LONG_SL hit after SHORT_SL. Loss: ${profit:.2f}")
                                        break

                            if outcome == 'SHORT_SL':  # If no exit in the long reversal
                                profit = -(0.2 + SPREAD) * 10 * position_lot_size
                                exit_price = renko_with_predictions.iloc[j]['close']
                                exit_time = renko_with_predictions.iloc[j]['datetime']
                            break

            # Time-based exit if no other exit condition met
            if profit == 0:
                if position_type == "LONG":
                    gross_profit = (renko_with_predictions.iloc[min(i + 10, len(renko_with_predictions) - 1)]['close'] - entry_price) * 10 * position_lot_size
                else:  # SHORT
                    gross_profit = (entry_price - renko_with_predictions.iloc[min(i + 10, len(renko_with_predictions) - 1)]['close']) * 10 * position_lot_size
                profit, trade_commission = apply_commission(gross_profit)
                outcome = 'TIME_EXIT'
                exit_price = renko_with_predictions.iloc[min(i + 10, len(renko_with_predictions) - 1)]['close']
                exit_time = renko_with_predictions.iloc[min(i + 10, len(renko_with_predictions) - 1)]['datetime']
                if trade_commission > 0:
                    log_message(f"Time exit taken. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                else:
                    log_message(f"Time exit taken. Loss: ${profit:.2f} (no commission on losses)")

            # Update account state
            previous_equity = equity
            equity += profit
            total_commission += trade_commission
            equity_history.append(equity)
            trade_outcomes.append(profit)

            # Safety check: Stop trading if balance goes negative
            if equity < 0:
                log_message(f"CRITICAL: Balance went negative (${equity:.2f}). Stopping trading immediately!")
                log_message(f"Trade #{trade_count} caused the negative balance.")
                break

            # Update win/loss streaks
            if profit > 0:
                win_streak += 1
                loss_streak = 0
                win_count += 1
            else:
                win_streak = 0
                loss_streak += 1

            # Log trade completion
            log_message(f"Trade #{trade_count} completed:")
            log_message(f"Entry Time: {entry_time}")
            log_message(f"Exit Time: {exit_time}")
            log_message(f"Outcome: {outcome}")
            log_message(f"Profit: ${profit:.2f}")
            log_message(f"Equity: ${previous_equity:.2f} -> ${equity:.2f}")

            # Record trade
            trades.append({
                'entry_time': entry_time,
                'entry_price': entry_price,
                'position_type': position_type,
                'volume': round(position_lot_size, 2),
                'exit_time': exit_time,
                'exit_price': exit_price,
                'outcome': outcome,
                'profit': round(profit, 2),
                'commission': round(trade_commission, 2),
                'balance': round(equity, 2),
                'risk_percentage': round(risk_percentage, 4)
            })

            # Update open volume
            open_volume -= position_lot_size

            # Move to the next bar after the exit
            i = renko_with_predictions.index.get_loc(renko_with_predictions[renko_with_predictions['datetime'] == exit_time].index[0]) + 1
        else:
            i += 1
    else:
        i += 1

# Save trading results
results_df = pd.DataFrame(trades)
results_df.to_csv(f"{log_dir}/xgboost_strategy_results_{timestamp}.csv", index=False)
log_message(f"Saved strategy results to {log_dir}/xgboost_strategy_results_{timestamp}.csv")

# Calculate performance metrics
win_rate = (results_df['profit'] > 0).mean() * 100 if len(results_df) > 0 else 0
profit_factor = results_df[results_df['profit'] > 0]['profit'].sum() / abs(results_df[results_df['profit'] < 0]['profit'].sum() + 1e-6) if len(results_df) > 0 else 0
win_percentage = (win_count / trade_count * 100) if trade_count > 0 else 0

# Final report
log_message("\n=== FINAL STRATEGY RESULTS ===")
log_message(f"Trades Executed: {trade_count}")
log_message(f"Final Balance: ${equity:.2f}")
log_message(f"Total Commission Paid: ${total_commission:.2f}")
log_message(f"Commission Rate: {COMMISSION_RATE*100:.1f}% on profits")
log_message(f"Win Rate: {win_rate:.2f}%")
log_message(f"Win Count: {win_count} of {trade_count} trades ({win_percentage:.2f}%)")
log_message(f"Profit Factor: {profit_factor:.2f}")

# Plot equity curve
plt.figure(figsize=(12, 6))
plt.plot(equity_history)
plt.title('Equity Curve')
plt.xlabel('Trade Number')
plt.ylabel('Equity ($)')
plt.grid(True)
plt.savefig(f"{log_dir}/equity_curve_{timestamp}.png")
log_message(f"Saved equity curve to {log_dir}/equity_curve_{timestamp}.png")

log_message("Strategy backtest completed successfully!")