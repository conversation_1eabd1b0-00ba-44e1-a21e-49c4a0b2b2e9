[2025-06-07 18:29:07] === XGBOOST MODEL TRAINING WITH UT BOT SIGNALS ===
[2025-06-07 18:29:07] Loading Step Index Renko data...
[2025-06-07 18:29:08] Loaded 130167 Renko bricks from 3-day dataset
[2025-06-07 18:29:08] Data range: 2025-06-04 18:00:33 to 2025-06-07 18:00:28
[2025-06-07 18:29:08] Generating UT Bot signals...
[2025-06-07 18:29:10] UT Bot signal generation completed in 1805.32ms
[2025-06-07 18:29:10] Generated UT Bot signals for 130167 data points
[2025-06-07 18:29:10] Starting vectorized feature engineering...
[2025-06-07 18:29:16] Vectorized feature engineering completed in 5992.97ms
[2025-06-07 18:29:16] Generated 44 features for 130167 data points
[2025-06-07 18:29:16] Combining UT Bot signals with vectorized features...
[2025-06-07 18:29:16] Added UT Bot features: ['xATRTrailingStop', 'pos', 'buy', 'sell', 'barbuy', 'barsell', 'xATR', 'ema_src']
[2025-06-07 18:29:16] Creating target labels with 3 period lookahead...
[2025-06-07 18:29:20] Target distribution: Short=0, Neutral=130167, Long=0
[2025-06-07 18:29:20] Target percentages: Short=0.0%, Neutral=100.0%, Long=0.0%
[2025-06-07 18:29:20] Preparing final dataset for training...
[2025-06-07 18:29:20] Final feature matrix shape: (130167, 52)
[2025-06-07 18:29:20] Target vector shape: (130167,)
[2025-06-07 18:29:20] Feature columns: 52
[2025-06-07 18:29:20] After removing lookahead samples: X=(130164, 52), y=(130164,)
[2025-06-07 18:29:20] Splitting data into train/test sets...
[2025-06-07 18:29:20] Training set: X=(104131, 52), y=(104131,)
[2025-06-07 18:29:20] Test set: X=(26033, 52), y=(26033,)
[2025-06-07 18:29:20] Training target distribution: Short=0, Neutral=104131, Long=0
[2025-06-07 18:29:20] Test target distribution: Short=0, Neutral=26033, Long=0
[2025-06-07 18:29:20] Training XGBoost model...
[2025-06-07 18:29:20] XGBoost parameters: {'objective': 'multi:softprob', 'num_class': 3, 'max_depth': 6, 'learning_rate': 0.1, 'n_estimators': 200, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'n_jobs': -1, 'eval_metric': 'mlogloss'}
