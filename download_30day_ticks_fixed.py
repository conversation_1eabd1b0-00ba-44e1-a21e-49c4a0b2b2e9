#!/usr/bin/env python3
"""
Fixed script to download 30 days of Step Index tick data
Uses the correct DataFrame conversion based on test results
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import time
import os

def download_30day_ticks():
    """Download 30 days of Step Index tick data with proper conversion"""
    print("📥 Step Index 30-Day Tick Download (Fixed)")
    print("=" * 50)
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"❌ MT5 init failed: {mt5.last_error()}")
        return False
    
    # Login
    login = 5749910
    password = "@Ripper25"
    server = "Deriv-Demo"
    
    if not mt5.login(login, password, server):
        print(f"❌ Login failed: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print(f"✅ Connected to {login} on {server}")
    
    # Symbol
    symbol = "Step Index.0"
    
    # Date range - last 30 days
    date_to = datetime.now()
    date_from = date_to - timedelta(days=30)
    
    print(f"📅 Downloading from {date_from} to {date_to}")
    print("⏱️  Estimated time: 10-15 minutes for 2.6M ticks")
    
    # Download in 5-day chunks to be safe
    chunk_days = 5
    all_dataframes = []
    
    current_date = date_from
    chunk_num = 1
    total_chunks = 6  # 30 days / 5 days per chunk
    
    start_time = time.time()
    
    while current_date < date_to:
        chunk_end = min(current_date + timedelta(days=chunk_days), date_to)
        
        print(f"\n📦 Chunk {chunk_num}/{total_chunks}: {current_date} to {chunk_end}")
        
        chunk_start = time.time()
        
        # Download chunk
        ticks = mt5.copy_ticks_range(symbol, current_date, chunk_end, mt5.COPY_TICKS_ALL)
        
        if ticks is None or len(ticks) == 0:
            print(f"  ⚠️  No data for chunk {chunk_num}")
            current_date = chunk_end
            chunk_num += 1
            continue
        
        chunk_download_time = time.time() - chunk_start
        print(f"  ✅ Downloaded {len(ticks):,} ticks in {chunk_download_time:.1f}s")
        
        # Convert to DataFrame using the correct method
        print(f"  🔄 Converting to DataFrame...")
        conversion_start = time.time()
        
        # Direct conversion works based on our test
        df_chunk = pd.DataFrame(ticks)
        
        # Verify we have the expected columns
        expected_cols = ['time', 'bid', 'ask', 'last', 'volume', 'time_msc', 'flags', 'volume_real']
        if list(df_chunk.columns) == expected_cols:
            print(f"  ✅ Correct columns: {list(df_chunk.columns)}")
        else:
            print(f"  ⚠️  Unexpected columns: {list(df_chunk.columns)}")
        
        # Add datetime column
        df_chunk['datetime'] = pd.to_datetime(df_chunk['time'], unit='s')
        
        # Optimize data types
        df_chunk['time'] = df_chunk['time'].astype('int64')
        df_chunk['bid'] = df_chunk['bid'].astype('float32')
        df_chunk['ask'] = df_chunk['ask'].astype('float32')
        df_chunk['last'] = df_chunk['last'].astype('float32')
        df_chunk['volume'] = df_chunk['volume'].astype('int32')
        df_chunk['time_msc'] = df_chunk['time_msc'].astype('int64')
        df_chunk['flags'] = df_chunk['flags'].astype('int32')
        df_chunk['volume_real'] = df_chunk['volume_real'].astype('float32')
        
        conversion_time = time.time() - conversion_start
        print(f"  ✅ Converted in {conversion_time:.1f}s")
        
        all_dataframes.append(df_chunk)
        
        # Progress update
        progress = (chunk_num / total_chunks) * 100
        elapsed = time.time() - start_time
        print(f"  📊 Progress: {progress:.1f}% | Elapsed: {elapsed/60:.1f}min")
        
        current_date = chunk_end
        chunk_num += 1
    
    # Combine all chunks
    print(f"\n🔗 Combining all chunks...")
    combine_start = time.time()
    
    final_df = pd.concat(all_dataframes, ignore_index=True)
    
    combine_time = time.time() - combine_start
    total_time = time.time() - start_time
    
    print(f"✅ Combined in {combine_time:.1f}s")
    print(f"📊 Total records: {len(final_df):,}")
    
    # Save to CSV
    timestamp = int(time.time())
    filename = f"step_index_30days_fixed_{timestamp}.csv"
    
    print(f"\n💾 Saving to {filename}...")
    save_start = time.time()
    
    final_df.to_csv(filename, index=False)
    
    save_time = time.time() - save_start
    file_size_mb = os.path.getsize(filename) / (1024 * 1024)
    
    # Final summary
    print(f"\n" + "=" * 60)
    print(f"✅ 30-DAY DOWNLOAD COMPLETED SUCCESSFULLY!")
    print(f"=" * 60)
    print(f"📁 File: {filename}")
    print(f"📊 Records: {len(final_df):,}")
    print(f"📅 Period: {final_df['datetime'].min()} to {final_df['datetime'].max()}")
    print(f"💰 Price range: {final_df['bid'].min():.1f} - {final_df['bid'].max():.1f}")
    print(f"📏 File size: {file_size_mb:.1f} MB")
    print(f"⏱️  Total time: {total_time/60:.1f} minutes")
    print(f"🔧 Columns: {list(final_df.columns)}")
    
    # Show sample data
    print(f"\n📋 SAMPLE DATA:")
    print(final_df.head(3))
    
    # Data quality check
    print(f"\n🔍 DATA QUALITY:")
    print(f"  Missing bid: {final_df['bid'].isna().sum()}")
    print(f"  Missing ask: {final_df['ask'].isna().sum()}")
    print(f"  Zero bid: {(final_df['bid'] == 0).sum()}")
    print(f"  Zero ask: {(final_df['ask'] == 0).sum()}")
    
    mt5.shutdown()
    return filename

if __name__ == "__main__":
    result = download_30day_ticks()
    if result:
        print(f"\n🎉 SUCCESS! File ready: {result}")
        print(f"🧱 Ready for Renko conversion!")
    else:
        print(f"\n❌ Download failed!")
