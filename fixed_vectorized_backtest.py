#!/usr/bin/env python3
"""
FIXED Ultra-Fast Vectorized UT Bot Backtest
Proper volume limits: MIN=0.20, MAX_PER_POS=50, MAX_TOTAL=200
"""

import pandas as pd
import numpy as np
from datetime import datetime
import time
from ut_bot_adaptation import UTBot

def fixed_vectorized_backtest():
    """
    Fixed vectorized backtest with proper volume constraints
    """
    print("FIXED Ultra-Fast Vectorized UT Bot Backtest")
    print("=" * 60)
    
    # Volume constraints
    MIN_VOL = 0.20
    MAX_VOL_PER_POS = 50.0
    MAX_TOTAL_VOL = 200.0
    BRICK_SIZE = 0.1
    COMMISSION_RATE = 0.15
    
    start_time = time.time()
    
    # Load data
    print("Loading Step Index Renko data...")
    renko_df = pd.read_csv('step_index_renko_0_1.csv')
    renko_df['datetime'] = pd.to_datetime(renko_df['datetime'])
    print(f"Loaded {len(renko_df)} Renko bricks")

    # Calculate time differences between bricks (for realistic delay simulation)
    renko_df['time_diff_ms'] = renko_df['datetime'].diff().dt.total_seconds() * 1000
    renko_df['time_diff_ms'].fillna(1000, inplace=True)  # Default 1s between bricks
    
    # Generate signals
    print("Generating UT Bot signals...")
    signal_start = time.time()
    ut_bot = UTBot(atr_period=10, sensitivity=1)
    signals_df = ut_bot.run(renko_df)
    signal_time = time.time() - signal_start
    
    buy_count = signals_df['buy'].sum()
    sell_count = signals_df['sell'].sum()
    print(f"Generated {buy_count} BUY and {sell_count} SELL signals in {signal_time:.3f}s")
    
    # Merge data
    data = pd.concat([renko_df, signals_df], axis=1)
    
    # Get signal indices
    buy_signals = data['buy'].fillna(False)
    sell_signals = data['sell'].fillna(False)
    signal_mask = buy_signals | sell_signals
    signal_indices = np.where(signal_mask)[0]
    
    # Initialize tracking
    equity = 10.0
    equity_history = [equity]
    trades = []
    open_volume = 0.0
    execution_stats = {
        'instant_fills': 0,
        'delayed_fills': 0,
        'price_improvements': 0,
        'price_deteriorations': 0,
        'avg_slippage': []
    }

    reversal_stats = {
        'total_reversals': 0,
        'reversal_wins': 0,
        'reversal_losses': 0,
        'reversal_profits': [],
        'reversal_slippage': [],
        'reversal_instant_fills': 0,
        'reversal_delayed_fills': 0,
        'bricks_to_tp': []  # Track how many bricks to hit TP
    }
    
    print("Running fixed vectorized backtest...")
    backtest_start = time.time()
    
    for signal_idx in signal_indices:
        if signal_idx >= len(data) - 25:  # Need room for delay + trade management
            break

        # Fixed risk calculation with proper volume limits
        risk_percentage = 0.04
        risk_amount = equity * risk_percentage
        price_risk = 2 * BRICK_SIZE
        ideal_lot_size = risk_amount / (price_risk * 10)

        # Apply strict volume constraints
        available_vol = MAX_TOTAL_VOL - open_volume
        lot_size = max(MIN_VOL, min(ideal_lot_size, MAX_VOL_PER_POS, available_vol))

        # Ensure we don't exceed limits
        if lot_size < MIN_VOL or available_vol < MIN_VOL or lot_size > MAX_VOL_PER_POS:
            continue

        # Signal details
        signal_time_dt = data['datetime'].iloc[signal_idx]
        signal_price = data['close'].iloc[signal_idx]
        position_type = "LONG" if buy_signals.iloc[signal_idx] else "SHORT"

        # Calculate realistic execution price with 922.8ms delay
        execution_price, execution_idx, fill_type = calculate_execution_price_vectorized(
            signal_idx, data, 922.8, signal_price
        )

        # Track execution statistics
        execution_stats[fill_type] += 1
        slippage = execution_price - signal_price if position_type == "LONG" else signal_price - execution_price
        execution_stats['avg_slippage'].append(slippage)

        if slippage > 0:
            execution_stats['price_improvements'] += 1
        elif slippage < 0:
            execution_stats['price_deteriorations'] += 1

        # Update open volume
        open_volume += lot_size

        # Calculate trade outcome from realistic execution point using ATR trailing stop and check for reversal signals
        trade_outcome = calculate_realistic_trade_outcome(
            execution_idx, data, position_type, execution_price, lot_size,
            COMMISSION_RATE, buy_signals, sell_signals, BRICK_SIZE
        )
        
        # Unpack the outcome
        profit = trade_outcome['profit']
        exit_price = trade_outcome['exit_price']
        exit_time = trade_outcome['exit_time']
        outcome = trade_outcome['outcome']

        # Track reversal statistics if reversal trade exists
        if 'reversal_trade' in trade_outcome:
            reversal_trade = trade_outcome['reversal_trade']
            reversal_stats['total_reversals'] += 1
            reversal_stats['reversal_profits'].append(reversal_trade['profit'])
            reversal_stats['reversal_slippage'].append(reversal_trade['slippage'])
            reversal_stats['bricks_to_tp'].append(reversal_trade.get('bricks_to_tp', 0))

            if reversal_trade['profit'] > 0:
                reversal_stats['reversal_wins'] += 1
            else:
                reversal_stats['reversal_losses'] += 1

            if reversal_trade['fill_type'] == 'instant_fills':
                reversal_stats['reversal_instant_fills'] += 1
            else:
                reversal_stats['reversal_delayed_fills'] += 1
        reversal_trade = trade_outcome.get('reversal_trade', None) # Get reversal trade details if exists

        # Update equity
        equity += profit
        equity_history.append(equity)
        
        # Update open volume
        open_volume -= lot_size
        open_volume = max(0, open_volume)  # Ensure non-negative
        
        # Record initial trade
        trade_record = {
            'signal_time': signal_time_dt,
            'signal_price': signal_price,
            'execution_price': execution_price,
            'slippage': round(slippage, 5),
            'fill_type': fill_type,
            'position_type': position_type,
            'volume': round(lot_size, 2),
            'exit_time': exit_time,
            'exit_price': exit_price,
            'outcome': outcome,
            'profit': round(profit, 2),
            'balance': round(equity, 2),
            'has_reversal': 'reversal_trade' in trade_outcome
        }

        # Add reversal trade details if exists
        if 'reversal_trade' in trade_outcome:
            reversal_trade = trade_outcome['reversal_trade']
            trade_record.update({
                'reversal_signal_price': reversal_trade['signal_price'],
                'reversal_execution_price': reversal_trade['execution_price'],
                'reversal_slippage': reversal_trade['slippage'],
                'reversal_fill_type': reversal_trade['fill_type'],
                'reversal_position_type': reversal_trade['position_type'],
                'reversal_exit_price': reversal_trade['exit_price'],
                'reversal_outcome': reversal_trade['outcome'],
                'reversal_profit': round(reversal_trade['profit'], 2)
            })

        trades.append(trade_record)

        # Handle reversal trade if it occurred
        if reversal_trade:
             # Update equity with reversal trade profit
            equity += reversal_trade['profit']
            equity_history.append(equity)

            # Record reversal trade
            trades.append({
                'signal_time': reversal_trade['signal_time'], # Reversal signal time
                'signal_price': reversal_trade['signal_price'], # Reversal signal price
                'execution_price': reversal_trade['execution_price'], # Reversal execution price
                'slippage': round(reversal_trade['slippage'], 5),
                'fill_type': reversal_trade['fill_type'],
                'position_type': reversal_trade['position_type'],
                'volume': round(reversal_trade['volume'], 2),
                'exit_time': reversal_trade['exit_time'],
                'exit_price': reversal_trade['exit_price'],
                'outcome': reversal_trade['outcome'],
                'profit': round(reversal_trade['profit'], 2),
                'balance': round(equity, 2) # Equity after reversal trade
            })
        
        # Safety check
        if equity < 0:
            print(f"Balance went negative: ${equity:.2f}")
            break
    
    backtest_time = time.time() - backtest_start
    total_time = time.time() - start_time
    
    print(f"Backtest completed in {backtest_time:.3f}s")
    print(f"Total execution time: {total_time:.3f}s")
    
    # Print results
    print_realistic_results(trades, equity, equity_history, renko_df, execution_stats, reversal_stats)

def calculate_execution_price_vectorized(signal_idx, data, delay_ms, signal_price):
    """
    Vectorized calculation of realistic execution price after delay
    """

    # Calculate cumulative time from signal
    if signal_idx + 1 >= len(data):
        return signal_price, signal_idx, 'instant_fills'

    # Get time differences and cumulative time
    time_diffs = data['time_diff_ms'].iloc[signal_idx+1:signal_idx+10].values
    if len(time_diffs) == 0:
        return signal_price, signal_idx, 'instant_fills'

    cumulative_time = np.cumsum(time_diffs)

    # Find where execution delay is reached
    delay_reached_idx = np.where(cumulative_time >= delay_ms)[0]

    if len(delay_reached_idx) == 0:
        # Delay longer than available data - use last available price
        execution_idx = min(signal_idx + len(time_diffs), len(data) - 1)
        execution_price = data['close'].iloc[execution_idx]
        return execution_price, execution_idx, 'delayed_fills'

    # Execution happens at first brick after delay
    execution_offset = delay_reached_idx[0] + 1
    execution_idx = signal_idx + execution_offset

    if execution_idx >= len(data):
        execution_idx = len(data) - 1

    execution_price = data['close'].iloc[execution_idx]

    # Determine if price moved during delay
    if execution_price == signal_price:
        return execution_price, execution_idx, 'instant_fills'
    else:
        return execution_price, execution_idx, 'delayed_fills'

def calculate_realistic_trade_outcome(execution_idx, data, position_type, execution_price,
                                    lot_size, COMMISSION_RATE, buy_signals, sell_signals, BRICK_SIZE):
    """
    Calculate trade outcome using ATR trailing stop and handle reversals with fixed TP.
    Returns a dictionary with initial trade outcome and optionally reversal trade outcome.
    """

    # Look ahead window (until end of data)
    end_idx = len(data)

    if execution_idx + 1 >= end_idx:
        return {'profit': 0, 'exit_price': execution_price, 'exit_time': data['datetime'].iloc[execution_idx], 'outcome': 'NO_DATA'}

    # Simulate trade progression from execution point
    for i in range(execution_idx + 1, end_idx):
        current_price = data['close'].iloc[i]
        current_time = data['datetime'].iloc[i]
        trailing_stop = data['xATRTrailingStop'].iloc[i]
        
        # Check for reversal signal at the current bar
        reversal_signal = False
        if position_type == "LONG" and sell_signals.iloc[i]:
            reversal_signal = True
        elif position_type == "SHORT" and buy_signals.iloc[i]:
            reversal_signal = True

        # Ensure trailing stop is not NaN if we are checking it
        if not reversal_signal and pd.isna(trailing_stop):
             continue # Skip if trailing stop is not available yet and no reversal signal

        exit_condition = False
        if position_type == "LONG":
            # Exit LONG if price drops below trailing stop OR a SELL signal occurs
            if current_price < trailing_stop or reversal_signal:
                exit_condition = True
        else:  # SHORT
            # Exit SHORT if price rises above trailing stop OR a BUY signal occurs
            if current_price > trailing_stop or reversal_signal:
                exit_condition = True

        if exit_condition:
            # Exit point for the initial trade
            exit_price = current_price
            exit_time = current_time

            if position_type == "LONG":
                gross_profit = (exit_price - execution_price) * 10 * lot_size
            else:
                gross_profit = (execution_price - exit_price) * 10 * lot_size

            # Apply commission only if profitable
            if gross_profit > 0:
                commission = gross_profit * COMMISSION_RATE
                profit = gross_profit - commission
            else:
                profit = gross_profit # No commission on losses

            initial_trade_outcome = {
                'profit': profit,
                'exit_price': exit_price,
                'exit_time': exit_time,
                'outcome': 'ATR_STOP_HIT' if not reversal_signal else f'{position_type}_REVERSAL_TRIGGERED'
            }

            # If a reversal signal triggered the exit, initiate a reversal trade
            if reversal_signal:
                reversal_position_type = "SHORT" if position_type == "LONG" else "LONG"

                # Apply 922.8ms execution delay to reversal entry
                reversal_signal_price = current_price
                reversal_execution_price, reversal_execution_idx, reversal_fill_type = calculate_execution_price_vectorized(
                    i, data, 922.8, reversal_signal_price
                )

                # Calculate reversal slippage
                reversal_slippage = reversal_execution_price - reversal_signal_price if reversal_position_type == "LONG" else reversal_signal_price - reversal_execution_price

                # Calculate reversal trade outcome with fixed 2-brick TP
                reversal_profit, reversal_exit_price, reversal_exit_time, reversal_outcome, bricks_to_tp = calculate_fixed_tp_outcome(
                    reversal_execution_idx, data, reversal_position_type, reversal_execution_price,
                    lot_size, 2, COMMISSION_RATE, BRICK_SIZE # 2 bricks TP
                )

                initial_trade_outcome['reversal_trade'] = {
                    'signal_time': current_time, # Reversal signal time is current time
                    'signal_price': reversal_signal_price, # Reversal signal price is current price
                    'execution_price': reversal_execution_price,
                    'slippage': round(reversal_slippage, 5), # Real slippage from execution delay
                    'fill_type': reversal_fill_type,
                    'position_type': reversal_position_type,
                    'volume': lot_size,
                    'exit_time': reversal_exit_time,
                    'exit_price': reversal_exit_price,
                    'outcome': reversal_outcome,
                    'profit': reversal_profit,
                    'bricks_to_tp': bricks_to_tp  # Track how many bricks to hit TP
                }

            return initial_trade_outcome

    # If loop finishes without hitting stop or reversal signal, consider it a time exit.
    exit_price = data['close'].iloc[end_idx-1]
    exit_time = data['datetime'].iloc[end_idx-1]

    if position_type == "LONG":
        gross_profit = (exit_price - execution_price) * 10 * lot_size
    else:
        gross_profit = (execution_price - exit_price) * 10 * lot_size

    if gross_profit > 0:
        commission = gross_profit * COMMISSION_RATE
        profit = gross_profit - commission
    else:
        profit = gross_profit

    return {'profit': profit, 'exit_price': exit_price, 'exit_time': exit_time, 'outcome': 'TIME_EXIT_NO_STOP_HIT'}

def calculate_fixed_tp_outcome(execution_idx, data, position_type, execution_price,
                                lot_size, tp_bricks, COMMISSION_RATE, BRICK_SIZE):
    """
    Calculate trade outcome with a fixed brick-based take profit (NO SL - track time to TP).
    """
    end_idx = len(data) # Look until the end of data

    if execution_idx + 1 >= end_idx:
        return 0, execution_price, data['datetime'].iloc[execution_idx], 'NO_DATA_FIXED_TP', 0

    # Track how many bricks it takes to hit TP
    bricks_to_tp = 0

    # Simulate trade progression from execution point
    for i in range(execution_idx + 1, end_idx):
        direction = data['direction'].iloc[i]
        current_price = data['close'].iloc[i]
        current_time = data['datetime'].iloc[i]
        bricks_to_tp += 1  # Count each brick

        if position_type == "LONG":
            if direction == 'up':
                tp_bricks -= 1
                if tp_bricks <= 0:
                    # TP hit
                    gross_profit = (2 * BRICK_SIZE) * 10 * lot_size # Fixed 2 bricks TP
                    commission = gross_profit * COMMISSION_RATE
                    profit = gross_profit - commission
                    return profit, current_price, current_time, 'LONG_TP_FIXED', bricks_to_tp
        else:  # SHORT
            if direction == 'down':
                tp_bricks -= 1
                if tp_bricks <= 0:
                    # TP hit
                    gross_profit = (2 * BRICK_SIZE) * 10 * lot_size # Fixed 2 bricks TP
                    commission = gross_profit * COMMISSION_RATE
                    profit = gross_profit - commission
                    return profit, current_price, current_time, 'SHORT_TP_FIXED', bricks_to_tp

    # If loop finishes without hitting TP, consider it a time exit.
    exit_price = data['close'].iloc[end_idx-1]
    exit_time = data['datetime'].iloc[end_idx-1]

    if position_type == "LONG":
        gross_profit = (exit_price - execution_price) * 10 * lot_size
    else:
        gross_profit = (execution_price - exit_price) * 10 * lot_size

    if gross_profit > 0:
        commission = gross_profit * COMMISSION_RATE
        profit = gross_profit - commission
    else:
        profit = gross_profit

    return profit, exit_price, exit_time, 'TIME_EXIT_FIXED_TP', bricks_to_tp


def print_realistic_results(trades, final_equity, equity_history, df, execution_stats, reversal_stats):
    """Print realistic results with execution analysis and reversal tracking"""
    print("\n" + "=" * 60)
    print("REALISTIC EXECUTION DELAY BACKTEST RESULTS")
    print("=" * 60)

    initial_equity = 10.0
    trades_df = pd.DataFrame(trades)

    print(f"Initial Balance:     ${initial_equity:.2f}")
    print(f"Final Balance:       ${final_equity:.2f}")
    print(f"Total Return:        ${final_equity - initial_equity:+.2f}")
    print(f"Return %:            {((final_equity / initial_equity) - 1) * 100:+.2f}%")

    if len(trades_df) > 0:
        winning_trades = trades_df[trades_df['profit'] > 0]
        losing_trades = trades_df[trades_df['profit'] <= 0]

        print(f"\nTRADE STATISTICS:")
        print(f"Total Trades:        {len(trades_df)}")
        print(f"Winning Trades:      {len(winning_trades)} ({len(winning_trades)/len(trades_df)*100:.1f}%)")
        print(f"Losing Trades:       {len(losing_trades)} ({len(losing_trades)/len(trades_df)*100:.1f}%)")

        if len(winning_trades) > 0:
            print(f"Avg Win:             ${winning_trades['profit'].mean():.2f}")
            print(f"Max Win:             ${winning_trades['profit'].max():.2f}")

        if len(losing_trades) > 0:
            print(f"Avg Loss:            ${losing_trades['profit'].mean():.2f}")
            print(f"Max Loss:            ${losing_trades['profit'].min():.2f}")

        # Execution delay impact analysis
        print(f"\nEXECUTION DELAY IMPACT (922.8ms):")
        print(f"Instant fills:       {execution_stats['instant_fills']}")
        print(f"Delayed fills:       {execution_stats['delayed_fills']}")
        print(f"Price improvements:  {execution_stats['price_improvements']}")
        print(f"Price deteriorations: {execution_stats['price_deteriorations']}")

        if execution_stats['avg_slippage']:
            avg_slippage = np.mean(execution_stats['avg_slippage'])
            print(f"Average slippage:    {avg_slippage:+.5f} points")
            print(f"Max slippage:        {max(execution_stats['avg_slippage']):+.5f} points")
            print(f"Min slippage:        {min(execution_stats['avg_slippage']):+.5f} points")

        # Reversal trade analysis
        print(f"\nREVERSAL TRADE ANALYSIS:")
        print(f"Total reversals:     {reversal_stats['total_reversals']}")
        print(f"Reversal wins:       {reversal_stats['reversal_wins']}")
        print(f"Reversal losses:     {reversal_stats['reversal_losses']}")

        if reversal_stats['total_reversals'] > 0:
            reversal_win_rate = (reversal_stats['reversal_wins'] / reversal_stats['total_reversals']) * 100
            print(f"Reversal win rate:   {reversal_win_rate:.1f}%")

            if reversal_stats['reversal_profits']:
                avg_reversal_profit = np.mean(reversal_stats['reversal_profits'])
                print(f"Avg reversal profit: ${avg_reversal_profit:+.2f}")
                print(f"Max reversal profit: ${max(reversal_stats['reversal_profits']):+.2f}")
                print(f"Min reversal profit: ${min(reversal_stats['reversal_profits']):+.2f}")

            if reversal_stats['reversal_slippage']:
                avg_reversal_slippage = np.mean(reversal_stats['reversal_slippage'])
                print(f"Avg reversal slippage: {avg_reversal_slippage:+.5f} points")

            print(f"Reversal instant fills: {reversal_stats['reversal_instant_fills']}")
            print(f"Reversal delayed fills: {reversal_stats['reversal_delayed_fills']}")

            # Brick timing analysis
            if reversal_stats['bricks_to_tp']:
                avg_bricks = np.mean(reversal_stats['bricks_to_tp'])
                max_bricks = max(reversal_stats['bricks_to_tp'])
                min_bricks = min(reversal_stats['bricks_to_tp'])
                print(f"\nREVERSAL TIMING ANALYSIS:")
                print(f"Avg bricks to hit 2-brick TP: {avg_bricks:.1f}")
                print(f"Max bricks to hit TP: {max_bricks}")
                print(f"Min bricks to hit TP: {min_bricks}")

                # Time estimation (assuming ~1 second per brick)
                avg_seconds = avg_bricks * 1.0
                max_seconds = max_bricks * 1.0
                print(f"Avg time to hit TP: ~{avg_seconds:.1f} seconds")
                print(f"Max time to hit TP: ~{max_seconds:.1f} seconds")

            # Calculate percentage of trades with reversals
            trades_with_reversals = sum(1 for trade in trades if trade.get('has_reversal', False))
            reversal_percentage = (trades_with_reversals / len(trades)) * 100
            print(f"Trades with reversals: {trades_with_reversals} ({reversal_percentage:.1f}%)")

        # Performance metrics
        if len(losing_trades) > 0:
            profit_factor = winning_trades['profit'].sum() / abs(losing_trades['profit'].sum())
            print(f"\nProfit Factor:       {profit_factor:.2f}")
        else:
            print(f"\nProfit Factor:       ∞ (no losses)")

        # Show sample trades with execution details
        print(f"\nSAMPLE TRADES WITH EXECUTION DELAY:")
        for _, trade in trades_df.head(5).iterrows():
            print(f"  {trade['position_type']} | Signal: {trade['signal_price']:.5f} | "
                  f"Fill: {trade['execution_price']:.5f} | Slip: {trade['slippage']:+.5f} | "
                  f"${trade['profit']:+7.2f} | {trade['fill_type']}")

    print("=" * 60)

if __name__ == "__main__":
    fixed_vectorized_backtest()
