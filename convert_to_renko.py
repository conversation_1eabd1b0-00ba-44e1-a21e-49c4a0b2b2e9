#!/usr/bin/env python3
"""
Convert Step Index 500 Ticks to 0.1 Renko Boxes
Convert raw tick data to Renko format matching stpRNG CSV structure

Input: step_index_500_ticks.csv (timestamp,datetime,bid,ask,mid,spread)
Output: step_index_renko_0_1.csv (timestamp,datetime,open,high,low,close,direction)
"""

import pandas as pd
import numpy as np
from datetime import datetime

class RenkoBuilder:
    """Build Renko charts from tick data"""
    
    def __init__(self, brick_size=0.1):
        self.brick_size = brick_size
        self.renko_data = []
        self.current_brick = None
        
    def add_tick(self, timestamp, price):
        """Add a tick and check if new Renko brick is formed"""
        if self.current_brick is None:
            # Initialize first brick
            self.current_brick = {
                'timestamp': timestamp,
                'open': price,
                'high': price,
                'low': price,
                'close': price,
                'direction': 'none'
            }
            return None
            
        # Check if price movement creates new brick(s)
        price_diff = price - self.current_brick['close']
        
        if abs(price_diff) >= self.brick_size:
            # Calculate number of bricks to create
            num_bricks = int(abs(price_diff) / self.brick_size)
            direction = 'up' if price_diff > 0 else 'down'
            
            new_bricks = []
            
            for i in range(num_bricks):
                if direction == 'up':
                    # Up brick
                    brick_open = self.current_brick['close']
                    brick_close = brick_open + self.brick_size
                    new_brick = {
                        'timestamp': timestamp,
                        'open': brick_open,
                        'high': brick_close,
                        'low': brick_open,
                        'close': brick_close,
                        'direction': 'up'
                    }
                else:
                    # Down brick
                    brick_open = self.current_brick['close']
                    brick_close = brick_open - self.brick_size
                    new_brick = {
                        'timestamp': timestamp,
                        'open': brick_open,
                        'high': brick_open,
                        'low': brick_close,
                        'close': brick_close,
                        'direction': 'down'
                    }
                
                new_bricks.append(new_brick)
                self.current_brick = new_brick
                
            return new_bricks
            
        else:
            # Update current brick high/low but don't close it
            self.current_brick['high'] = max(self.current_brick['high'], price)
            self.current_brick['low'] = min(self.current_brick['low'], price)
            return None

def convert_ticks_to_renko():
    """Convert Step Index ticks to Renko boxes"""
    print("🧱 Converting Step Index Ticks to 0.1 Renko Boxes")
    print("=" * 60)
    
    # Read tick data
    print("📖 Reading tick data...")
    try:
        # Try to find the fixed 30-day file first
        df = pd.read_csv('step_index_30days_fixed_1749321357.csv')
        print(f"✅ Loaded {len(df):,} ticks from 30-day fixed dataset")
        dataset_name = "30days_fixed"
    except FileNotFoundError:
        try:
            # Fallback to original 30-day file
            df = pd.read_csv('step_index_30days_1749314546.csv')
            print(f"✅ Loaded {len(df):,} ticks from 30-day dataset")
            dataset_name = "30days"
        except FileNotFoundError:
            try:
                # Fallback to 3-day file
                df = pd.read_csv('mt5-live-trading/step_index_3days_1749312033.csv')
                print(f"✅ Loaded {len(df):,} ticks from 3-day dataset")
                dataset_name = "3days"
            except FileNotFoundError:
                print("❌ No Step Index tick data files found!")
                print("Available files should be:")
                print("  - step_index_30days_fixed_1749321357.csv (preferred)")
                print("  - step_index_30days_1749314546.csv (fallback)")
                print("  - mt5-live-trading/step_index_3days_1749312033.csv (fallback)")
                print("Run the tick collection script first.")
                return False
    
    # Initialize Renko builder
    brick_size = 0.1
    renko = RenkoBuilder(brick_size)
    
    print(f"🧱 Building Renko chart with {brick_size} brick size...")
    
    all_renko_bricks = []
    tick_count = 0
    brick_count = 0
    
    # Check available columns and determine price column
    print(f"📋 Available columns: {list(df.columns)}")

    # Determine timestamp column
    if 'time' in df.columns:
        timestamp_col = 'time'
    elif 'timestamp' in df.columns:
        timestamp_col = 'timestamp'
    else:
        print("❌ No timestamp column found!")
        return False

    # Determine price column (prefer bid, fallback to last, then ask)
    if 'bid' in df.columns:
        price_col = 'bid'
        print(f"📊 Using 'bid' prices for Renko conversion")
    elif 'last' in df.columns:
        price_col = 'last'
        print(f"📊 Using 'last' prices for Renko conversion")
    elif 'ask' in df.columns:
        price_col = 'ask'
        print(f"📊 Using 'ask' prices for Renko conversion")
    else:
        print("❌ No price column found (bid/last/ask)!")
        return False

    # Process each tick
    for _, row in df.iterrows():
        tick_count += 1
        timestamp = int(row[timestamp_col])
        price = float(row[price_col])
        
        # Add tick to Renko builder
        new_bricks = renko.add_tick(timestamp, price)
        
        if new_bricks:
            for brick in new_bricks:
                brick_count += 1
                
                # Format brick data to match stpRNG CSV format
                renko_row = {
                    'timestamp': brick['timestamp'],
                    'datetime': datetime.fromtimestamp(brick['timestamp']).strftime('%Y-%m-%d %H:%M:%S'),
                    'open': round(brick['open'], 1),
                    'high': round(brick['high'], 1),
                    'low': round(brick['low'], 1),
                    'close': round(brick['close'], 1),
                    'direction': brick['direction']
                }
                
                all_renko_bricks.append(renko_row)
                
                # Show progress for significant bricks
                if brick_count % 5 == 0 or brick_count <= 10:
                    print(f"Brick #{brick_count:2d} | "
                          f"Time: {renko_row['datetime'][-8:]} | "
                          f"Price: {renko_row['open']:.1f} → {renko_row['close']:.1f} | "
                          f"Dir: {renko_row['direction']}")
    
    print(f"\n📊 Renko Conversion Summary:")
    print(f"  Input ticks: {tick_count}")
    print(f"  Output bricks: {brick_count}")
    print(f"  Brick size: {brick_size}")
    print(f"  Conversion rate: {brick_count/tick_count*100:.1f}% (bricks per tick)")
    
    if brick_count == 0:
        print("⚠️ No Renko bricks generated!")
        print("This might happen if price movement is less than brick size.")
        return False
    
    # Save to CSV
    output_file = f'step_index_renko_0_1_{dataset_name}.csv'
    print(f"\n💾 Saving to {output_file}...")

    renko_df = pd.DataFrame(all_renko_bricks)
    renko_df.to_csv(output_file, index=False)
    
    print(f"✅ Saved {len(renko_df)} Renko bricks")
    
    # Show sample data
    print(f"\n📋 Sample Renko Data (first 10 bricks):")
    print("-" * 80)
    for i, brick in enumerate(all_renko_bricks[:10]):
        print(f"{i+1:2d}. {brick['datetime']} | "
              f"{brick['open']:7.1f} → {brick['close']:7.1f} | "
              f"{brick['direction']:4s}")
    
    if len(all_renko_bricks) > 10:
        print("    ...")
        print(f"    (and {len(all_renko_bricks)-10} more bricks)")
    
    # Analyze price movement
    if all_renko_bricks:
        first_price = all_renko_bricks[0]['open']
        last_price = all_renko_bricks[-1]['close']
        total_movement = last_price - first_price
        
        up_bricks = len([b for b in all_renko_bricks if b['direction'] == 'up'])
        down_bricks = len([b for b in all_renko_bricks if b['direction'] == 'down'])
        
        print(f"\n📈 Price Movement Analysis:")
        print(f"  Start price: {first_price:.1f}")
        print(f"  End price: {last_price:.1f}")
        print(f"  Net movement: {total_movement:+.1f}")
        print(f"  Up bricks: {up_bricks}")
        print(f"  Down bricks: {down_bricks}")
        print(f"  Trend: {'Bullish' if up_bricks > down_bricks else 'Bearish' if down_bricks > up_bricks else 'Sideways'}")
    
    print(f"\n🎯 Renko conversion completed successfully!")
    print(f"📁 Output file: {output_file}")
    
    return True

def compare_with_original():
    """Compare our Renko data with original stpRNG format"""
    print(f"\n🔍 Comparing with original stpRNG format...")
    
    try:
        # Read our generated Renko data
        our_renko = pd.read_csv('step_index_renko_0_1.csv')
        print(f"✅ Our Renko data: {len(our_renko)} bricks")
        
        # Read original stpRNG data (first few rows for comparison)
        original_renko = pd.read_csv('../qt-meta/stpRNG_7days_renko_0_1.csv', nrows=20)
        print(f"✅ Original stpRNG data: {len(original_renko)} bricks (sample)")
        
        print(f"\n📊 Format Comparison:")
        print(f"Our columns:      {list(our_renko.columns)}")
        print(f"Original columns: {list(original_renko.columns)}")
        
        if list(our_renko.columns) == list(original_renko.columns):
            print("✅ Column formats match perfectly!")
        else:
            print("⚠️ Column formats differ")
            
        print(f"\n📋 Sample Data Comparison:")
        print("Our Renko (first 3):")
        for i in range(min(3, len(our_renko))):
            row = our_renko.iloc[i]
            print(f"  {row['datetime']} | {row['open']:.1f}→{row['close']:.1f} | {row['direction']}")
            
        print("Original stpRNG (first 3):")
        for i in range(min(3, len(original_renko))):
            row = original_renko.iloc[i]
            print(f"  {row['datetime']} | {row['open']:.1f}→{row['close']:.1f} | {row['direction']}")
            
    except Exception as e:
        print(f"⚠️ Comparison failed: {e}")

def main():
    """Main conversion function"""
    success = convert_ticks_to_renko()
    
    if success:
        compare_with_original()
        print(f"\n🎉 RENKO CONVERSION COMPLETED!")
        print(f"Ready for UT Bot backtesting and live trading!")
    else:
        print(f"\n❌ RENKO CONVERSION FAILED!")

if __name__ == "__main__":
    main()
