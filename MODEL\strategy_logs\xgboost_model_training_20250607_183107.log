[2025-06-07 18:31:07] === XGBOOST MODEL TRAINING WITH UT BOT SIGNALS ===
[2025-06-07 18:31:07] Loading Step Index Renko data...
[2025-06-07 18:31:07] Loaded 130167 Renko bricks from 3-day dataset
[2025-06-07 18:31:07] Data range: 2025-06-04 18:00:33 to 2025-06-07 18:00:28
[2025-06-07 18:31:07] Generating UT Bot signals...
[2025-06-07 18:31:09] UT Bot signal generation completed in 1660.60ms
[2025-06-07 18:31:09] Generated UT Bot signals for 130167 data points
[2025-06-07 18:31:09] Starting vectorized feature engineering...
[2025-06-07 18:31:15] Vectorized feature engineering completed in 6091.18ms
[2025-06-07 18:31:15] Generated 44 features for 130167 data points
[2025-06-07 18:31:15] Combining UT Bot signals with vectorized features...
[2025-06-07 18:31:15] Added UT Bot features: ['xATRTrailingStop', 'pos', 'buy', 'sell', 'barbuy', 'barsell', 'xATR', 'ema_src']
[2025-06-07 18:31:15] Creating target labels with 3 period lookahead...
[2025-06-07 18:31:19] Target distribution: Short=65686, Long=64478
[2025-06-07 18:31:19] Target percentages: Short=50.5%, Long=49.5%
[2025-06-07 18:31:19] Preparing final dataset for training...
[2025-06-07 18:31:19] Final feature matrix shape: (130167, 52)
[2025-06-07 18:31:19] Target vector shape: (130167,)
[2025-06-07 18:31:19] Feature columns: 52
[2025-06-07 18:31:19] After removing lookahead samples: X=(130164, 52), y=(130164,)
[2025-06-07 18:31:19] Splitting data into train/test sets...
[2025-06-07 18:31:19] Training set: X=(104131, 52), y=(104131,)
[2025-06-07 18:31:19] Test set: X=(26033, 52), y=(26033,)
[2025-06-07 18:31:19] Training target distribution: Short=52549, Long=51582
[2025-06-07 18:31:19] Test target distribution: Short=13137, Long=12896
[2025-06-07 18:31:19] Training XGBoost model...
[2025-06-07 18:31:19] XGBoost parameters: {'objective': 'binary:logistic', 'max_depth': 6, 'learning_rate': 0.1, 'n_estimators': 200, 'subsample': 0.8, 'colsample_bytree': 0.8, 'random_state': 42, 'n_jobs': -1, 'eval_metric': 'logloss'}
[2025-06-07 18:31:27] Model training completed in 7.48 seconds
[2025-06-07 18:31:27] Making predictions on test set...
[2025-06-07 18:31:27] === MODEL PERFORMANCE METRICS ===
[2025-06-07 18:31:27] Accuracy: 0.6038
[2025-06-07 18:31:27] Precision: 0.6037
[2025-06-07 18:31:27] Recall: 0.6038
[2025-06-07 18:31:27] F1-Score: 0.6037
[2025-06-07 18:31:27] 
=== DETAILED CLASSIFICATION REPORT ===
[2025-06-07 18:31:27] 
              precision    recall  f1-score   support

   Short (0)       0.61      0.61      0.61     13137
    Long (1)       0.60      0.59      0.60     12896

    accuracy                           0.60     26033
   macro avg       0.60      0.60      0.60     26033
weighted avg       0.60      0.60      0.60     26033

[2025-06-07 18:31:27] 
=== CONFUSION MATRIX ===
[2025-06-07 18:31:27] Confusion Matrix:
[[8070 5067]
 [5248 7648]]
[2025-06-07 18:31:27] 
=== FEATURE IMPORTANCE ANALYSIS ===
[2025-06-07 18:31:27] Top 20 most important features:
[2025-06-07 18:31:27]  1. direction                : 0.585220
[2025-06-07 18:31:27]  2. price_change             : 0.253477
[2025-06-07 18:31:27]  3. up_streak                : 0.007919
[2025-06-07 18:31:27]  4. barbuy                   : 0.004225
[2025-06-07 18:31:27]  5. price_pct_change         : 0.004140
[2025-06-07 18:31:27]  6. xATRTrailingStop         : 0.003699
[2025-06-07 18:31:27]  7. price_pct_change_20      : 0.003590
[2025-06-07 18:31:27]  8. minute                   : 0.003525
[2025-06-07 18:31:27]  9. price_pct_change_30      : 0.003501
[2025-06-07 18:31:27] 10. barsell                  : 0.003489
[2025-06-07 18:31:27] 11. price_pct_change_15      : 0.003449
[2025-06-07 18:31:27] 12. ema_src                  : 0.003425
[2025-06-07 18:31:27] 13. volatility_30            : 0.003417
[2025-06-07 18:31:27] 14. price_momentum_15        : 0.003401
[2025-06-07 18:31:27] 15. sell                     : 0.003390
[2025-06-07 18:31:27] 16. price_pct_change_5       : 0.003356
[2025-06-07 18:31:27] 17. pos                      : 0.003349
[2025-06-07 18:31:27] 18. volatility_15            : 0.003333
[2025-06-07 18:31:27] 19. price                    : 0.003326
[2025-06-07 18:31:27] 20. price_pct_change_10      : 0.003303
[2025-06-07 18:31:27] Saved feature importance to MODEL/strategy_logs/feature_importance_20250607_183107.csv
[2025-06-07 18:31:27] Saving model to MODEL/xgboost_model_utbot_20250607_183107.pkl...
[2025-06-07 18:31:27] Model saved successfully to MODEL/xgboost_model_utbot_20250607_183107.pkl
[2025-06-07 18:31:27] Saved model metadata to MODEL/model_metadata_utbot_20250607_183107.json
[2025-06-07 18:31:29] Saved feature importance plot to MODEL/strategy_logs/feature_importance_20250607_183107.png
[2025-06-07 18:31:30] Saved confusion matrix plot to MODEL/strategy_logs/confusion_matrix_20250607_183107.png
[2025-06-07 18:31:30] 
=== SAMPLE PREDICTIONS ===
[2025-06-07 18:31:30] Sample 1:
[2025-06-07 18:31:30]   Actual: Long (1)
[2025-06-07 18:31:30]   Predicted: Long (1)
[2025-06-07 18:31:30]   Probabilities: Short=0.435, Long=0.565
[2025-06-07 18:31:30] Sample 2:
[2025-06-07 18:31:30]   Actual: Long (1)
[2025-06-07 18:31:30]   Predicted: Short (0)
[2025-06-07 18:31:30]   Probabilities: Short=0.541, Long=0.459
[2025-06-07 18:31:30] Sample 3:
[2025-06-07 18:31:30]   Actual: Short (0)
[2025-06-07 18:31:30]   Predicted: Long (1)
[2025-06-07 18:31:30]   Probabilities: Short=0.410, Long=0.590
[2025-06-07 18:31:30] Sample 4:
[2025-06-07 18:31:30]   Actual: Short (0)
[2025-06-07 18:31:30]   Predicted: Short (0)
[2025-06-07 18:31:30]   Probabilities: Short=0.604, Long=0.396
[2025-06-07 18:31:30] Sample 5:
[2025-06-07 18:31:30]   Actual: Short (0)
[2025-06-07 18:31:30]   Predicted: Short (0)
[2025-06-07 18:31:30]   Probabilities: Short=0.612, Long=0.388
[2025-06-07 18:31:30] 
=== MODEL TRAINING COMPLETED SUCCESSFULLY ===
[2025-06-07 18:31:30] Model file: MODEL/xgboost_model_utbot_20250607_183107.pkl
[2025-06-07 18:31:30] Metadata file: MODEL/model_metadata_utbot_20250607_183107.json
[2025-06-07 18:31:30] Training log: MODEL/strategy_logs/xgboost_model_training_20250607_183107.log
[2025-06-07 18:31:30] Total features: 52 (including 8 UT Bot features)
[2025-06-07 18:31:30] Model performance: 60.4% accuracy, 0.6037 F1-score
