#!/usr/bin/env python3
"""
UT Bot BUY ONLY Backtest - Simple version like original ut_bot_backtest.py
Tests the performance of only taking BUY signals from UT Bot
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import sys
from datetime import datetime
import os
import math

# Add the qt-meta directory to the path
sys.path.append('qt-meta')
from ut_bot_buy_only import UTBotBuyOnly

# Set up logging
log_dir = "strategy_logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"{log_dir}/ut_bot_buy_only_backtest_{timestamp}.log"

def log_message(message):
    """Log message to file and print to console"""
    timestamp_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp_str}] {message}"
    print(log_msg)
    with open(log_filename, 'a') as f:
        f.write(log_msg + '\n')

log_message("=== UT BOT BUY ONLY TRADING STRATEGY BACKTEST ===")

# Load the Renko data for backtesting
log_message("Loading Step Index 30-day Renko data...")
renko_df = pd.read_csv('step_index_renko_0_1_30days_fixed.csv')
renko_df['datetime'] = pd.to_datetime(renko_df['datetime'])
log_message(f"Renko data shape: {renko_df.shape}")

# Initialize UTBot BUY ONLY with atr_period=1 and sensitivity=1
ut_bot = UTBotBuyOnly(atr_period=1, sensitivity=1)
log_message("Running UT Bot BUY ONLY signal generation...")

# Run UT Bot on the data
utbot_signals = ut_bot.run(renko_df)
log_message(f"UT Bot signals generated: {utbot_signals.shape}")

# Merge signals with Renko data
renko_with_signals = pd.concat([renko_df, utbot_signals], axis=1)

# Count signals
buy_signals = utbot_signals['buy'].sum()
sell_signals = utbot_signals['sell'].sum()  # Should be 0 for buy-only
log_message(f"Buy signals: {buy_signals}")
log_message(f"Sell signals: {sell_signals} (should be 0 for buy-only)")

# Backtest Parameters
BRICK_SIZE = 0.1
SPREAD = 0.0
COMMISSION_RATE = 0.15  # 15% commission on profits
MIN_VOL = 0.20  # Global minimum lot size
MAX_VOL_PER_POS = 50.0
MAX_TOTAL_VOL = 200.0

def apply_commission(profit):
    """Apply 15% commission on profits only. Losses are not charged commission."""
    if profit > 0:
        commission = profit * COMMISSION_RATE
        net_profit = profit - commission
        return net_profit, commission
    else:
        return profit, 0.0  # No commission on losses

# Dynamic risk management function
def calculate_dynamic_risk(equity, equity_history, win_streak, loss_streak, recent_outcomes=None):
    """Calculate dynamic risk percentage based on multiple factors"""
    base_risk = 0.04  # Base risk percentage

    # 1. Streak-based adjustment
    if win_streak >= 5:
        streak_factor = 1.3
    elif win_streak >= 3:
        streak_factor = 1.15
    elif loss_streak >= 3:
        streak_factor = 0.7
    elif loss_streak >= 1:
        streak_factor = 0.85
    else:
        streak_factor = 1.0

    # 2. Equity milestone adjustment
    if equity < 1000:
        equity_factor = 1.0
    elif equity < 10000:
        equity_factor = 0.95
    elif equity < 100000:
        equity_factor = 0.9
    elif equity < 1000000:
        equity_factor = 0.85
    else:
        equity_factor = 0.8

    # 3. Recent performance adjustment
    if recent_outcomes and len(recent_outcomes) >= 20:
        win_rate = sum(1 for t in recent_outcomes[-20:] if t > 0) / 20
        if win_rate > 0.9:
            performance_factor = 1.2
        elif win_rate > 0.8:
            performance_factor = 1.1
        elif win_rate < 0.5:
            performance_factor = 0.8
        else:
            performance_factor = 1.0
    else:
        performance_factor = 1.0

    # 4. Drawdown protection
    if len(equity_history) > 1:
        peak = max(equity_history)
        drawdown = (peak - equity) / peak
        if drawdown > 0.2:
            drawdown_factor = 0.6
        elif drawdown > 0.1:
            drawdown_factor = 0.8
        else:
            drawdown_factor = 1.0
    else:
        drawdown_factor = 1.0

    # Calculate final risk percentage
    risk = base_risk * streak_factor * equity_factor * performance_factor * drawdown_factor

    # Ensure risk stays within reasonable bounds
    return max(0.01, min(risk, 0.06))  # Cap between 1% and 6%

# Position sizing function
def calculate_position_size(risk_amount, price_risk, max_per_position, max_total_vol, current_open_vol):
    """Calculate optimal position allocation respecting volume constraints"""
    # Calculate ideal lot size
    ideal_lot_size = risk_amount / (price_risk * 10)

    # Available volume within total limit
    available_vol = max_total_vol - current_open_vol

    # Check if we need multiple positions
    if ideal_lot_size <= max_per_position:
        # Single position is sufficient
        lot_size = min(ideal_lot_size, max_per_position, available_vol)
        num_positions = 1 if lot_size >= MIN_VOL else 0
    else:
        # Need multiple positions
        max_possible_vol = min(ideal_lot_size, available_vol)
        if max_possible_vol < MIN_VOL:
            return 0, 0

        num_positions = math.ceil(max_possible_vol / max_per_position)
        lot_size = min(max_per_position, max_possible_vol / num_positions)
        
        # Ensure minimum lot size is respected
        if lot_size < MIN_VOL:
            return 0, 0

    return num_positions, lot_size

# Initialize account state
log_message("Starting BUY ONLY sequential backtest...")
equity = 10.0  # Starting with $10
open_volume = 0
equity_history = [equity]
win_streak = 0
loss_streak = 0
trade_outcomes = []
win_count = 0
trade_count = 0
total_commission = 0.0
signal_engine_active = True  # Signal engine control

# Trading results
trades = []

# Sequential backtest loop - ONE TRADE AT A TIME (BUY ONLY)
i = 50  # Start after enough data for UT Bot calculations
while i < len(renko_with_signals) - 10:
    current_row = renko_with_signals.iloc[i]
    
    # Only process signals when signal engine is active
    if not signal_engine_active:
        i += 1
        continue
    
    # Check for BUY signal only (sell signals are disabled)
    if current_row['buy']:
        # TURN OFF SIGNAL ENGINE - Only one trade at a time
        signal_engine_active = False
        
        # Calculate dynamic risk
        risk_percentage = calculate_dynamic_risk(
            equity,
            equity_history,
            win_streak,
            loss_streak,
            trade_outcomes
        )
        
        # Calculate risk amount and price risk
        risk_amount = equity * risk_percentage
        price_risk = 0.2 + SPREAD  # 2 bricks + spread
        
        # Calculate position size
        num_positions, lot_size = calculate_position_size(
            risk_amount,
            price_risk,
            MAX_VOL_PER_POS,
            MAX_TOTAL_VOL,
            open_volume
        )
        
        if num_positions > 0 and lot_size >= MIN_VOL:
            # Execute LONG trade
            entry_time = current_row['datetime']
            entry_price = current_row['close']
            position_lot_size = lot_size
            open_volume += position_lot_size
            trade_count += 1
            
            log_message(f"BUY ONLY Trade #{trade_count} executed - LONG at {entry_time}")
            log_message(f"Entry price: {entry_price:.5f}, Volume: {position_lot_size:.2f}")
            
            # Brick counting for trade lifecycle (LONG only)
            tp_bricks = 5  # Take profit at 5 bricks
            sl_bricks = 2  # Stop loss at 2 bricks
            
            # Initialize trade outcome variables
            profit = 0
            trade_commission = 0.0
            outcome = None
            exit_price = entry_price
            exit_time = entry_time
            
            # Simulate LONG trade with brick counting
            for j in range(i + 1, min(i + 60, len(renko_with_signals))):  # Max 60 ticks
                move = renko_with_signals.iloc[j]['direction']
                current_price = renko_with_signals.iloc[j]['close']
                
                if move == 'up':
                    tp_bricks -= 1
                    if tp_bricks == 0:
                        gross_profit = (0.5 - SPREAD) * 10 * position_lot_size
                        profit, trade_commission = apply_commission(gross_profit)
                        outcome = 'LONG_TP'
                        exit_price = current_price
                        exit_time = renko_with_signals.iloc[j]['datetime']
                        log_message(f"LONG_TP hit. Gross: ${gross_profit:.2f}, Net: ${profit:.2f}")
                        break
                elif move == 'down':
                    sl_bricks -= 1
                    if sl_bricks == 0:
                        # For BUY ONLY strategy, we exit on SL without reversal
                        profit = -(0.2 + SPREAD) * 10 * position_lot_size
                        outcome = 'LONG_SL'
                        exit_price = current_price
                        exit_time = renko_with_signals.iloc[j]['datetime']
                        log_message(f"LONG_SL hit. Loss: ${profit:.2f}")
                        break
            
            # Time-based exit if no other exit condition met
            if profit == 0:
                gross_profit = (renko_with_signals.iloc[min(i + 30, len(renko_with_signals) - 1)]['close'] - entry_price) * 10 * position_lot_size
                profit, trade_commission = apply_commission(gross_profit)
                outcome = 'TIME_EXIT'
                exit_price = renko_with_signals.iloc[min(i + 30, len(renko_with_signals) - 1)]['close']
                exit_time = renko_with_signals.iloc[min(i + 30, len(renko_with_signals) - 1)]['datetime']
                log_message(f"Time exit taken. Net: ${profit:.2f}")
            
            # Update account state
            previous_equity = equity
            equity += profit
            total_commission += trade_commission
            equity_history.append(equity)
            trade_outcomes.append(profit)
            
            # Safety check: Stop trading if balance goes negative
            if equity < 0:
                log_message(f"CRITICAL: Balance went negative (${equity:.2f}). Stopping trading!")
                break
            
            # Update win/loss streaks
            if profit > 0:
                win_streak += 1
                loss_streak = 0
                win_count += 1
            else:
                win_streak = 0
                loss_streak += 1
            
            # Log trade completion
            log_message(f"BUY ONLY Trade #{trade_count} completed:")
            log_message(f"Outcome: {outcome}")
            log_message(f"Profit: ${profit:.2f}")
            log_message(f"Equity: ${previous_equity:.2f} -> ${equity:.2f}")
            
            # Record trade
            trades.append({
                'entry_time': entry_time,
                'entry_price': entry_price,
                'position_type': 'LONG',
                'volume': round(position_lot_size, 2),
                'exit_time': exit_time,
                'exit_price': exit_price,
                'outcome': outcome,
                'profit': round(profit, 2),
                'commission': round(trade_commission, 2),
                'balance': round(equity, 2),
                'risk_percentage': round(risk_percentage, 4)
            })
            
            # Update open volume
            open_volume -= position_lot_size
            
            # TURN SIGNAL ENGINE BACK ON after trade completion
            signal_engine_active = True
            
            # Move to the next bar after the exit
            i = renko_with_signals.index.get_loc(renko_with_signals[renko_with_signals['datetime'] == exit_time].index[0]) + 1
        else:
            # No valid position size, turn signal engine back on
            signal_engine_active = True
            i += 1
    else:
        i += 1

# Save trading results
results_df = pd.DataFrame(trades)
results_file = f"{log_dir}/ut_bot_buy_only_results_{timestamp}.csv"
results_df.to_csv(results_file, index=False)
log_message(f"Saved BUY ONLY strategy results to {results_file}")

# Calculate performance metrics
if len(results_df) > 0:
    win_rate = (results_df['profit'] > 0).mean() * 100
    profit_factor = results_df[results_df['profit'] > 0]['profit'].sum() / abs(results_df[results_df['profit'] < 0]['profit'].sum() + 1e-6)
    win_percentage = (win_count / trade_count * 100) if trade_count > 0 else 0

    # Calculate additional metrics
    total_return = ((equity - 10) / 10) * 100  # Percentage return from $10 start
    max_equity = max(equity_history)
    max_drawdown = ((max_equity - min(equity_history)) / max_equity) * 100 if max_equity > 0 else 0

    # Average trade metrics
    avg_win = results_df[results_df['profit'] > 0]['profit'].mean() if len(results_df[results_df['profit'] > 0]) > 0 else 0
    avg_loss = results_df[results_df['profit'] < 0]['profit'].mean() if len(results_df[results_df['profit'] < 0]) > 0 else 0

else:
    win_rate = 0
    profit_factor = 0
    win_percentage = 0
    total_return = 0
    max_drawdown = 0
    avg_win = 0
    avg_loss = 0

# Final report
log_message("\n=== UT BOT BUY ONLY BACKTEST RESULTS ===")
log_message(f"Strategy: BUY ONLY (no sell signals)")
log_message(f"Dataset: 30-day Step Index Renko")
log_message(f"Minimum Lot Size: {MIN_VOL}")
log_message(f"Commission Rate: {COMMISSION_RATE*100:.1f}% on profits")
log_message("")
log_message("=== PERFORMANCE METRICS ===")
log_message(f"Trades Executed: {trade_count}")
log_message(f"Starting Balance: $10.00")
log_message(f"Final Balance: ${equity:.2f}")
log_message(f"Total Return: {total_return:.2f}%")
log_message(f"Total Commission Paid: ${total_commission:.2f}")
log_message(f"Win Rate: {win_rate:.2f}%")
log_message(f"Win Count: {win_count} of {trade_count} trades ({win_percentage:.2f}%)")
log_message(f"Profit Factor: {profit_factor:.2f}")
log_message(f"Max Drawdown: {max_drawdown:.2f}%")
log_message(f"Average Win: ${avg_win:.2f}")
log_message(f"Average Loss: ${avg_loss:.2f}")

log_message("")
log_message("=== SIGNAL ANALYSIS ===")
log_message(f"Total Buy Signals: {buy_signals}")
log_message(f"Total Sell Signals: {sell_signals} (disabled)")
log_message(f"Signals Converted to Trades: {trade_count}")
log_message(f"Signal-to-Trade Conversion: {(trade_count / buy_signals * 100) if buy_signals > 0 else 0:.1f}%")

# Plot equity curve
plt.figure(figsize=(12, 8))

# Subplot 1: Equity Curve
plt.subplot(2, 2, 1)
plt.plot(equity_history)
plt.title('Equity Curve - UT Bot BUY ONLY Strategy')
plt.xlabel('Trade Number')
plt.ylabel('Equity ($)')
plt.grid(True)

# Subplot 2: Trade Distribution
plt.subplot(2, 2, 2)
if len(results_df) > 0:
    outcomes = results_df['outcome'].value_counts()
    plt.pie(outcomes.values, labels=outcomes.index, autopct='%1.1f%%')
    plt.title('Trade Outcome Distribution')

# Subplot 3: Profit Distribution
plt.subplot(2, 2, 3)
if len(results_df) > 0:
    plt.hist(results_df['profit'], bins=20, alpha=0.7, edgecolor='black')
    plt.axvline(x=0, color='red', linestyle='--', alpha=0.7)
    plt.title('Profit Distribution')
    plt.xlabel('Profit ($)')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)

# Subplot 4: Cumulative Returns
plt.subplot(2, 2, 4)
if len(results_df) > 0:
    cumulative_returns = np.cumsum(results_df['profit'])
    plt.plot(cumulative_returns)
    plt.title('Cumulative Returns')
    plt.xlabel('Trade Number')
    plt.ylabel('Cumulative Profit ($)')
    plt.grid(True, alpha=0.3)

plt.tight_layout()
equity_curve_file = f"{log_dir}/ut_bot_buy_only_equity_curve_{timestamp}.png"
plt.savefig(equity_curve_file, dpi=300, bbox_inches='tight')
log_message(f"Saved equity curve to {equity_curve_file}")

log_message("")
log_message("=== BUY ONLY BACKTEST COMPLETED ===")
log_message(f"Results file: {results_file}")
log_message(f"Equity curve: {equity_curve_file}")
log_message(f"Log file: {log_filename}")

print(f"\n🎯 UT Bot BUY ONLY Backtest Completed!")
print(f"📊 Final Balance: ${equity:.2f} (Return: {total_return:.2f}%)")
print(f"📈 Win Rate: {win_rate:.1f}% ({win_count}/{trade_count} trades)")
print(f"💰 Profit Factor: {profit_factor:.2f}")
print(f"📁 Results saved to: {results_file}")

if equity < 0:
    print("⚠️  WARNING: BUY ONLY strategy ended with negative balance!")
elif total_return > 100:
    print("🚀 Excellent performance! BUY ONLY strategy doubled the account!")
elif total_return > 50:
    print("✅ Good performance! BUY ONLY strategy achieved solid returns!")
elif total_return > 0:
    print("📈 Positive performance! BUY ONLY strategy was profitable!")
else:
    print("📉 BUY ONLY strategy was not profitable in this period.")
