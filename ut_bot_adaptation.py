import pandas as pd
import numpy as np

class UTBot:
    def __init__(self, atr_period=1, sensitivity=1, use_heikin_ashi=False):
        self.a = sensitivity
        self.c = atr_period
        self.h = use_heikin_ashi
        self.xATRTrailingStop_history = []
        self.pos_history = []
        self.true_range_history = []
        self.atr_history = []

    def _calculate_true_range_vectorized(self, highs, lows, closes):
        """Vectorized True Range calculation - EXACT same logic as original"""
        n = len(closes)
        true_ranges = np.zeros(n)

        # First bar: TR = high - low (no previous close)
        true_ranges[0] = highs[0] - lows[0]

        # Remaining bars: TR = max(high - low, abs(high - prev_close), abs(low - prev_close))
        if n > 1:
            prev_closes = closes[:-1]  # Previous closes
            current_highs = highs[1:]  # Current highs
            current_lows = lows[1:]    # Current lows

            range1 = current_highs - current_lows
            range2 = np.abs(current_highs - prev_closes)
            range3 = np.abs(current_lows - prev_closes)

            true_ranges[1:] = np.maximum(np.maximum(range1, range2), range3)

        return true_ranges

    def _calculate_atr_vectorized(self, true_ranges):
        """Vectorized ATR calculation - EXACT same logic as original"""
        n = len(true_ranges)
        atrs = np.full(n, np.nan)

        # Not enough data for initial ATR
        if n < self.c:
            return atrs

        # Initial ATR is SMA of first 'c' true ranges (EXACT same logic)
        atrs[self.c - 1] = np.mean(true_ranges[:self.c])

        # Wilder's smoothing for remaining values (EXACT same logic)
        for i in range(self.c, n):
            prev_atr = atrs[i - 1]
            atrs[i] = (prev_atr * (self.c - 1) + true_ranges[i]) / self.c

        return atrs

    def _nz(self, value, default_value):
        return default_value if pd.isna(value) else value

    def _crossover(self, series1_prev, series1_current, series2_prev, series2_current):
        return series1_prev < series2_prev and series1_current > series2_current

    def run(self, df):
        # Ensure DataFrame has 'open', 'high', 'low', 'close'
        # For simplicity, assuming 'close' is the primary source for now.
        # If Heikin Ashi is truly needed, it would require calculating HA candles first.
        # Given h=False, src will always be close.

        closes = df['close'].values
        highs = df['high'].values
        lows = df['low'].values
        n = len(df)

        # Vectorized True Range and ATR calculation
        true_ranges = self._calculate_true_range_vectorized(highs, lows, closes)
        atrs = self._calculate_atr_vectorized(true_ranges)

        # Update histories for compatibility
        self.true_range_history = true_ranges.tolist()
        self.atr_history = atrs.tolist()

        # Initialize output arrays
        xATRTrailingStop = np.full(n, np.nan)
        pos = np.zeros(n)
        ema_src = closes.copy()  # ema(src, 1) is just src

        # Sequential calculation for xATRTrailingStop (must preserve exact logic)
        for i in range(n):
            src = closes[i]
            xATR = atrs[i]
            nLoss = self.a * xATR

            prev_xATRTrailingStop = self._nz(xATRTrailingStop[i-1] if i > 0 else np.nan, 0)
            src_prev = closes[i-1] if i > 0 else np.nan

            current_xATRTrailingStop = np.nan
            if not pd.isna(src) and not pd.isna(nLoss):
                if src > prev_xATRTrailingStop and src_prev > prev_xATRTrailingStop:
                    current_xATRTrailingStop = max(prev_xATRTrailingStop, src - nLoss)
                elif src < prev_xATRTrailingStop and src_prev < prev_xATRTrailingStop:
                    current_xATRTrailingStop = min(prev_xATRTrailingStop, src + nLoss)
                elif src > prev_xATRTrailingStop:
                    current_xATRTrailingStop = src - nLoss
                else: # src < prev_xATRTrailingStop
                    current_xATRTrailingStop = src + nLoss

            xATRTrailingStop[i] = current_xATRTrailingStop

        # Update history for compatibility
        self.xATRTrailingStop_history = xATRTrailingStop.tolist()

        # Sequential calculation for pos (must preserve exact logic)
        for i in range(n):
            src = closes[i]
            src_prev = closes[i-1] if i > 0 else np.nan
            prev_xATRTrailingStop = xATRTrailingStop[i-1] if i > 0 else np.nan

            prev_pos = self._nz(pos[i-1] if i > 0 else np.nan, 0)
            current_pos = prev_pos

            if not pd.isna(src) and not pd.isna(src_prev) and not pd.isna(prev_xATRTrailingStop):
                if src_prev < prev_xATRTrailingStop and src > xATRTrailingStop[i]:
                    current_pos = 1
                elif src_prev > prev_xATRTrailingStop and src < xATRTrailingStop[i]:
                    current_pos = -1

            pos[i] = current_pos

        # Update history for compatibility
        self.pos_history = pos.tolist()

        # Vectorized crossover calculations
        above = np.zeros(n, dtype=bool)
        below = np.zeros(n, dtype=bool)

        # Crossover logic (vectorized where possible)
        for i in range(1, n):
            if (not pd.isna(ema_src[i]) and not pd.isna(xATRTrailingStop[i]) and
                not pd.isna(ema_src[i-1]) and not pd.isna(xATRTrailingStop[i-1])):
                above[i] = self._crossover(ema_src[i-1], ema_src[i], xATRTrailingStop[i-1], xATRTrailingStop[i])
                below[i] = self._crossover(xATRTrailingStop[i-1], xATRTrailingStop[i], ema_src[i-1], ema_src[i])

        # Vectorized signal generation
        buy = (closes > xATRTrailingStop) & above
        sell = (closes < xATRTrailingStop) & below
        barbuy = closes > xATRTrailingStop
        barsell = closes < xATRTrailingStop

        # Create results dictionary
        signals = {
            'xATRTrailingStop': xATRTrailingStop,
            'pos': pos,
            'buy': buy,
            'sell': sell,
            'barbuy': barbuy,
            'barsell': barsell,
            'xATR': atrs,
            'ema_src': ema_src
        }

        return pd.DataFrame(signals, index=df.index)

# Example Usage:
# if __name__ == "__main__":
#     # Create dummy data for demonstration
#     data = {
#         'open': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109],
#         'high': [101, 102, 103, 104, 105, 106, 107, 108, 109, 110],
#         'low': [99, 100, 101, 102, 103, 104, 105, 106, 107, 108],
#         'close': [100.5, 101.5, 102.5, 103.5, 104.5, 105.5, 106.5, 107.5, 108.5, 109.5]
#     }
#     df = pd.DataFrame(data)

#     # Initialize UTBot with atr_period=1 and sensitivity=1 as requested
#     ut_bot = UTBot(atr_period=1, sensitivity=1)
#     results_df = ut_bot.run(df)

#     print("UT Bot Adaptation Results:")
#     print(results_df)