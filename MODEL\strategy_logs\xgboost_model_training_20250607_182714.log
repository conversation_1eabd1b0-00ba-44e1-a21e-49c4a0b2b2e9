[2025-06-07 18:27:14] === XGBOOST MODEL TRAINING WITH UT BOT SIGNALS ===
[2025-06-07 18:27:14] Loading Step Index Renko data...
[2025-06-07 18:27:14] Loaded 130167 Renko bricks from 3-day dataset
[2025-06-07 18:27:14] Data range: 2025-06-04 18:00:33 to 2025-06-07 18:00:28
[2025-06-07 18:27:14] Generating UT Bot signals...
[2025-06-07 18:27:16] UT Bot signal generation completed in 1513.99ms
[2025-06-07 18:27:16] Generated UT Bot signals for 130167 data points
[2025-06-07 18:27:16] Starting vectorized feature engineering...
[2025-06-07 18:27:21] Vectorized feature engineering completed in 5191.26ms
[2025-06-07 18:27:21] Generated 44 features for 130167 data points
[2025-06-07 18:27:21] Combining UT Bot signals with vectorized features...
[2025-06-07 18:27:21] Added UT Bot features: ['xATRTrailingStop', 'pos', 'buy', 'sell', 'barbuy', 'barsell', 'xATR', 'ema_src']
[2025-06-07 18:27:21] Creating target labels with 5 period lookahead...
[2025-06-07 18:27:23] Target distribution: Short=0, Neutral=130167, Long=0
[2025-06-07 18:27:23] Preparing final dataset for training...
[2025-06-07 18:27:23] Final feature matrix shape: (130167, 52)
[2025-06-07 18:27:23] Target vector shape: (130167,)
[2025-06-07 18:27:23] Feature columns: 52
[2025-06-07 18:27:23] After removing lookahead samples: X=(130162, 52), y=(130162,)
[2025-06-07 18:27:23] Splitting data into train/test sets...
[2025-06-07 18:27:24] Training set: X=(104129, 52), y=(104129,)
[2025-06-07 18:27:24] Test set: X=(26033, 52), y=(26033,)
