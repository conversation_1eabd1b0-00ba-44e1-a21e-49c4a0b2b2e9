#!/usr/bin/env python3
"""
UT Bot Python Implementation - Modified to run on Step Index Renko Data
Uses the EXACT same logic from ut_bot_python.py but adapted for Renko data format
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Optional
import os
import glob

class UTBot:
    """
    UT Bot (Ultimate Trend Bot) Python implementation
    Replicates the Pine Script logic with same parameters and functionality
    EXACT SAME LOGIC - just adapted for Renko data input
    """
    
    def __init__(self, key_value: float = 1.0, atr_period: int = 10, use_heikin_ashi: bool = False):
        """
        Initialize UT Bot with parameters matching Pine Script version
        
        Args:
            key_value: Sensitivity factor (default 1.0) - higher = less sensitive
            atr_period: ATR calculation period (default 10)
            use_heikin_ashi: Use Heikin Ashi candles instead of regular price
        """
        self.key_value = key_value
        self.atr_period = atr_period
        self.use_heikin_ashi = use_heikin_ashi
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
        """Calculate Average True Range - EXACT SAME LOGIC"""
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return true_range.rolling(window=self.atr_period).mean()
    
    def heikin_ashi_transform(self, open_price: pd.Series, high: pd.Series, 
                            low: pd.Series, close: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
        """Transform regular OHLC to Heikin Ashi - EXACT SAME LOGIC"""
        ha_close = (open_price + high + low + close) / 4
        ha_open = pd.Series(index=close.index, dtype=float)
        ha_high = pd.Series(index=close.index, dtype=float)
        ha_low = pd.Series(index=close.index, dtype=float)
        
        ha_open.iloc[0] = (open_price.iloc[0] + close.iloc[0]) / 2
        
        for i in range(1, len(close)):
            ha_open.iloc[i] = (ha_open.iloc[i-1] + ha_close.iloc[i-1]) / 2
            ha_high.iloc[i] = max(high.iloc[i], ha_open.iloc[i], ha_close.iloc[i])
            ha_low.iloc[i] = min(low.iloc[i], ha_open.iloc[i], ha_close.iloc[i])
        
        # Set first values
        ha_high.iloc[0] = max(high.iloc[0], ha_open.iloc[0], ha_close.iloc[0])
        ha_low.iloc[0] = min(low.iloc[0], ha_open.iloc[0], ha_close.iloc[0])
        
        return ha_open, ha_high, ha_low, ha_close
    
    def calculate_signals(self, open_price: pd.Series, high: pd.Series, 
                         low: pd.Series, close: pd.Series) -> pd.DataFrame:
        """
        Calculate UT Bot signals - EXACT SAME LOGIC as original
        """
        # Use Heikin Ashi if specified
        if self.use_heikin_ashi:
            _, ha_high, ha_low, ha_close = self.heikin_ashi_transform(open_price, high, low, close)
            src = ha_close
            atr_high, atr_low = ha_high, ha_low
        else:
            src = close
            atr_high, atr_low = high, low
        
        # Calculate ATR and loss
        xATR = self.calculate_atr(atr_high, atr_low, src)
        nLoss = self.key_value * xATR
        
        # Initialize arrays
        xATRTrailingStop = pd.Series(index=src.index, dtype=float)
        pos = pd.Series(index=src.index, dtype=int)
        
        # Calculate ATR Trailing Stop (replicating Pine Script logic)
        xATRTrailingStop.iloc[0] = src.iloc[0] - nLoss.iloc[0]
        
        for i in range(1, len(src)):
            prev_stop = xATRTrailingStop.iloc[i-1]
            curr_src = src.iloc[i]
            prev_src = src.iloc[i-1]
            curr_loss = nLoss.iloc[i]
            
            if not pd.isna(prev_stop):
                if curr_src > prev_stop and prev_src > prev_stop:
                    # Uptrend: trailing stop moves up
                    xATRTrailingStop.iloc[i] = max(prev_stop, curr_src - curr_loss)
                elif curr_src < prev_stop and prev_src < prev_stop:
                    # Downtrend: trailing stop moves down
                    xATRTrailingStop.iloc[i] = min(prev_stop, curr_src + curr_loss)
                elif curr_src > prev_stop:
                    # Switch to uptrend
                    xATRTrailingStop.iloc[i] = curr_src - curr_loss
                else:
                    # Switch to downtrend
                    xATRTrailingStop.iloc[i] = curr_src + curr_loss
            else:
                xATRTrailingStop.iloc[i] = curr_src - curr_loss
        
        # Calculate position (replicating Pine Script pos logic)
        pos.iloc[0] = 0
        
        for i in range(1, len(src)):
            prev_stop = xATRTrailingStop.iloc[i-1]
            curr_stop = xATRTrailingStop.iloc[i]
            prev_src = src.iloc[i-1]
            curr_src = src.iloc[i]
            
            if not pd.isna(prev_stop):
                if prev_src < prev_stop and curr_src > curr_stop:
                    pos.iloc[i] = 1  # Long position
                elif prev_src > prev_stop and curr_src < curr_stop:
                    pos.iloc[i] = -1  # Short position
                else:
                    pos.iloc[i] = pos.iloc[i-1]  # Hold previous position
            else:
                pos.iloc[i] = 0
        
        # Calculate EMA and crossovers (replicating Pine Script logic)
        ema = src.ewm(span=1, adjust=False).mean()  # EMA with period 1 = no smoothing
        
        # Crossover logic
        above = (ema > xATRTrailingStop) & (ema.shift(1) <= xATRTrailingStop.shift(1))
        below = (xATRTrailingStop > ema) & (xATRTrailingStop.shift(1) <= ema.shift(1))
        
        # Buy/Sell signals (exact Pine Script logic)
        buy = (src > xATRTrailingStop) & above
        sell = (src < xATRTrailingStop) & below
        
        # Bar colors
        barbuy = src > xATRTrailingStop
        barsell = src < xATRTrailingStop
        
        # Create results DataFrame
        results = pd.DataFrame({
            'close': src,
            'atr': xATR,
            'trailing_stop': xATRTrailingStop,
            'position': pos,
            'ema': ema,
            'buy_signal': buy,
            'sell_signal': sell,
            'bullish_bar': barbuy,
            'bearish_bar': barsell
        })
        
        return results
    
    def get_current_signal(self, results: pd.DataFrame) -> str:
        """Get the current signal state - EXACT SAME LOGIC"""
        if results.empty:
            return "No data"
        
        last_pos = results['position'].iloc[-1]
        last_buy = results['buy_signal'].iloc[-1]
        last_sell = results['sell_signal'].iloc[-1]
        
        if last_buy:
            return "BUY"
        elif last_sell:
            return "SELL"
        elif last_pos == 1:
            return "BULLISH"
        elif last_pos == -1:
            return "BEARISH"
        else:
            return "NEUTRAL"

def load_step_index_renko_data(file_path: str) -> pd.DataFrame:
    """
    Load Step Index Renko data from CSV file
    Expected format: datetime,open,high,low,close,direction,volume
    """
    print(f"Loading Step Index Renko data from: {file_path}")
    
    try:
        # Load the CSV file
        renko_df = pd.read_csv(file_path)
        
        # Convert datetime column
        renko_df['datetime'] = pd.to_datetime(renko_df['datetime'])
        renko_df.set_index('datetime', inplace=True)
        
        print(f"✅ Loaded {len(renko_df)} Renko bricks")
        print(f"📊 Date range: {renko_df.index[0]} to {renko_df.index[-1]}")
        print(f"📊 Price range: {renko_df['close'].min():.1f} - {renko_df['close'].max():.1f}")
        print(f"📊 Columns: {list(renko_df.columns)}")
        
        return renko_df
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def run_vectorized_backtest_only():
    """
    Load existing signals and run ONLY the vectorized backtest
    Skip signal generation completely for speed
    """

    print("\n⚡ LOADING EXISTING SIGNALS FOR VECTORIZED BACKTEST")
    print("=" * 60)

    # Load Step Index Renko data
    renko_data = load_step_index_renko_data('step_index_renko_0_1_30days_fixed.csv')

    if renko_data is None:
        print("❌ Failed to load data. Exiting.")
        return None, None

    # Try to load existing signals file
    signal_files = glob.glob('ut_bot_step_index_results_*.csv')

    if signal_files:
        # Use the most recent signals file
        latest_file = max(signal_files, key=os.path.getctime)
        print(f"📊 Loading existing signals from: {latest_file}")

        try:
            combined_data = pd.read_csv(latest_file)
            combined_data['datetime'] = pd.to_datetime(combined_data['datetime'])
            combined_data.set_index('datetime', inplace=True)

            # Extract just the signal columns
            signal_columns = ['buy_signal', 'sell_signal']
            if all(col in combined_data.columns for col in signal_columns):
                results = combined_data[signal_columns]

                total_buy_signals = results['buy_signal'].sum()
                total_sell_signals = results['sell_signal'].sum()

                print(f"✅ Loaded existing signals:")
                print(f"📈 BUY signals: {total_buy_signals}")
                print(f"📉 SELL signals: {total_sell_signals}")
                print(f"⚡ Skipping signal generation - going straight to backtest!")

                return renko_data, results
            else:
                print(f"❌ Signal columns not found in {latest_file}")

        except Exception as e:
            print(f"❌ Error loading signals: {e}")

    print("❌ No existing signals found. Please run signal generation first.")
    return None, None

def buy_only_trading_simulation(renko_data, signals, starting_balance=10.0, execution_delay_ms=922.8):
    """
    BUY ONLY trading simulation with 3 bricks target and 2 box stop loss
    Logic: BUY signal → LONG → 3 bricks TP OR 2 bricks SL → Close
    No reversals, no SELL signals processed
    """

    print(f"\n⚡ BUY ONLY TRADING SIMULATION")
    print(f"=" * 50)
    print(f"Starting Balance: ${starting_balance}")
    print(f"Execution Delay: {execution_delay_ms}ms")
    print(f"Logic: BUY ONLY → Target 3 bricks (+0.3) OR SL 2 bricks (-0.2)")
    print(f"No reversals, no SELL signals")
    print(f"=" * 50)

    import time
    start_time = time.perf_counter()

    # Merge signals with data (EXACT same as vectorized_ut_bot_backtest.py)
    data = pd.concat([renko_data, signals], axis=1)

    # Get BUY signal indices only (ignore SELL signals)
    buy_signals = data['buy_signal'].fillna(False)
    buy_signal_indices = np.where(buy_signals)[0]

    if len(buy_signal_indices) == 0:
        print("❌ No BUY signals found")
        return {'starting_balance': starting_balance, 'final_balance': starting_balance, 'total_return': 0}

    print(f"📊 Processing {len(buy_signal_indices)} BUY signals with vectorization...")
    print(f"📊 Ignoring SELL signals (BUY ONLY strategy)")

    # Pre-calculate arrays (EXACT same as vectorized_ut_bot_backtest.py)
    prices = data['close'].values
    timestamps = data['datetime'].values if 'datetime' in data.columns else data.index.values

    # Initialize tracking
    trades = []
    equity = starting_balance
    equity_history = [equity]

    # Trading parameters
    min_vol = 0.20

    # VECTORIZED BUY ONLY strategy - ultra-fast processing
    print(f"🚀 Vectorizing {len(buy_signal_indices)} BUY trades...")

    # Filter signals that have enough room for monitoring
    valid_signals = buy_signal_indices[buy_signal_indices < len(prices) - 20]

    if len(valid_signals) == 0:
        print("❌ No valid BUY signals with enough data")
        return {'starting_balance': starting_balance, 'final_balance': starting_balance, 'total_return': 0}

    # Vectorized execution delay (1 tick forward)
    execution_indices = np.minimum(valid_signals + 1, len(prices) - 1)
    entry_prices = prices[execution_indices]
    entry_times = timestamps[valid_signals]

    # Vectorized TP/SL targets
    tp_targets = entry_prices + (3 * 0.1)  # 3 bricks up
    sl_targets = entry_prices - (2 * 0.1)  # 2 bricks down

    # Initialize result arrays
    num_trades = len(valid_signals)
    exit_prices = np.zeros(num_trades)
    exit_indices = np.zeros(num_trades, dtype=int)
    outcomes = np.full(num_trades, 'TIME_EXIT', dtype='U10')

    # Vectorized exit detection
    for i, (signal_idx, entry_price, tp_target, sl_target) in enumerate(zip(valid_signals, entry_prices, tp_targets, sl_targets)):
        execution_idx = execution_indices[i]

        # Look ahead 20 bars for exit
        end_idx = min(execution_idx + 21, len(prices))
        future_prices = prices[execution_idx + 1:end_idx]

        if len(future_prices) == 0:
            # No future data
            exit_prices[i] = entry_price
            exit_indices[i] = execution_idx
            outcomes[i] = 'TIME_EXIT'
            continue

        # Vectorized TP/SL detection
        tp_hits = np.where(future_prices >= tp_target)[0]
        sl_hits = np.where(future_prices <= sl_target)[0]

        if len(tp_hits) > 0 and (len(sl_hits) == 0 or tp_hits[0] < sl_hits[0]):
            # TP hit first
            exit_prices[i] = tp_target
            exit_indices[i] = execution_idx + 1 + tp_hits[0]
            outcomes[i] = 'TP'
        elif len(sl_hits) > 0:
            # SL hit first
            exit_prices[i] = sl_target
            exit_indices[i] = execution_idx + 1 + sl_hits[0]
            outcomes[i] = 'SL'
        else:
            # Time exit
            exit_prices[i] = future_prices[-1]
            exit_indices[i] = end_idx - 1
            outcomes[i] = 'TIME_EXIT'

    # Vectorized profit calculation with compounding
    profit_per_unit = exit_prices - entry_prices

    # Calculate compounded profits
    equity_array = np.zeros(num_trades + 1)
    equity_array[0] = equity

    for i in range(num_trades):
        profit = profit_per_unit[i] * (equity_array[i] / entry_prices[i])
        equity_array[i + 1] = equity_array[i] + profit

    # Final equity
    equity = equity_array[-1]

    # Create trades list
    for i in range(num_trades):
        trades.append({
            'entry_time': entry_times[i],
            'exit_time': timestamps[exit_indices[i]],
            'position': 'LONG',
            'entry_price': entry_prices[i],
            'exit_price': exit_prices[i],
            'profit': profit_per_unit[i] * (equity_array[i] / entry_prices[i]),
            'balance': equity_array[i + 1],
            'outcome': outcomes[i]
        })

    equity_history.extend(equity_array[1:].tolist())

    # Vectorized outcome statistics
    tp_count = np.sum(outcomes == 'TP')
    sl_count = np.sum(outcomes == 'SL')
    time_count = np.sum(outcomes == 'TIME_EXIT')

    print(f"✅ Vectorized {num_trades} trades:")
    print(f"   📈 TP hits: {tp_count}")
    print(f"   📉 SL hits: {sl_count}")
    print(f"   ⏰ Time exits: {time_count}")

    # No final position to close in BUY ONLY strategy (each trade is complete)

    # Calculate statistics (EXACT same as vectorized_ut_bot_backtest.py)
    execution_time = (time.perf_counter() - start_time) * 1000  # ms

    trade_count = len(trades)
    if trade_count > 0:
        # Convert to numpy for faster calculations
        profits = np.array([t['profit'] for t in trades])

        win_count = np.sum(profits > 0)
        win_rate = (win_count / trade_count) * 100

        winning_profits = profits[profits > 0]
        losing_profits = profits[profits <= 0]

        if len(losing_profits) > 0:
            profit_factor = np.sum(winning_profits) / abs(np.sum(losing_profits))
        else:
            profit_factor = float('inf')
    else:
        win_count = 0
        win_rate = 0
        profit_factor = 0

    total_return = ((equity - starting_balance) / starting_balance) * 100

    # Print results (EXACT same format as vectorized_ut_bot_backtest.py)
    print(f"\n💰 BUY ONLY TRADING RESULTS")
    print(f"=" * 50)
    print(f"Starting Balance: ${starting_balance:.2f}")
    print(f"Final Balance: ${equity:.2f}")
    print(f"Total Return: {total_return:.2f}%")
    print(f"")
    print(f"📊 TRADE STATISTICS")
    print(f"Total Trades: {trade_count}")
    print(f"Winning Trades: {win_count}")
    print(f"Losing Trades: {trade_count - win_count}")
    print(f"Win Rate: {win_rate:.2f}%")
    print(f"Profit Factor: {profit_factor:.2f}")
    print(f"")
    print(f"🎯 STRATEGY DETAILS")
    print(f"Strategy: BUY ONLY")
    print(f"Target: +3 bricks (+0.3 points)")
    print(f"Stop Loss: -2 bricks (-0.2 points)")
    print(f"No reversals, no SELL signals")
    print(f"")
    print(f"⚡ PERFORMANCE")
    print(f"Execution Time: {execution_time:.2f}ms")
    print(f"BUY Signals Processed: {len(buy_signal_indices)}")
    print(f"Speed: {len(buy_signal_indices)/execution_time*1000:.0f} signals/second")

    return {
        'starting_balance': starting_balance,
        'final_balance': equity,
        'total_return': total_return,
        'trade_count': trade_count,
        'win_count': win_count,
        'win_rate': win_rate,
        'profit_factor': profit_factor,
        'execution_time_ms': execution_time,
        'trades': trades,
        'balance_history': equity_history
    }

if __name__ == "__main__":
    print("🚀 UT Bot Step Index Renko Analysis")
    print("=" * 50)
    print("EXACT same logic as original ut_bot_python.py")
    print("Same parameters: key_value=1.0, atr_period=10")
    print("=" * 50)

    # Run ONLY the vectorized backtest (skip signal generation)
    renko_data, results = run_vectorized_backtest_only()

    if renko_data is not None and results is not None:
        print(f"\n✅ Signal Analysis complete!")
        print(f"📊 Data: {len(renko_data)} Renko bricks")
        print(f"📊 Results: {len(results)} signal points")

        # Run BUY ONLY trading simulation with 3 bricks TP / 2 bricks SL
        trading_results = buy_only_trading_simulation(
            renko_data,
            results,
            starting_balance=10.0,  # Start with $10
            execution_delay_ms=922.8  # Broker execution delay
        )

        # Save detailed results
        output_file = f"ut_bot_step_index_results_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv"
        combined_results = pd.concat([renko_data, results], axis=1)
        combined_results.to_csv(output_file)

        # Save results
        print(f"💾 Signals saved to: {output_file}")

        print(f"\n🎯 UT BOT MONEY MAKING RESULTS:")
        print(f"💰 Turned ${trading_results['starting_balance']:.2f} into ${trading_results['final_balance']:.2f}")
        print(f"📈 Total Return: {trading_results['total_return']:.2f}%")
        print(f"🏆 Win Rate: {trading_results['win_rate']:.2f}%")
        print(f"⚡ With 922.8ms execution delay included!")

    else:
        print("❌ Analysis failed!")
