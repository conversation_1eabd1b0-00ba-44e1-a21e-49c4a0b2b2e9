#!/usr/bin/env python3
"""
Deriv.com WebSocket API Test Script
Subscribe to stepRNG 100 (stpRNG) live tick data

Requirements:
pip install websockets asyncio

Usage:
python deriv_stprng_test.py
"""

import asyncio
import websockets
import json
import logging
from datetime import datetime
import signal
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DerivTickSubscriber:
    def __init__(self, app_id, symbol="stpRNG"):
        self.app_id = app_id
        self.symbol = symbol
        self.websocket = None
        self.running = False
        self.ping_task = None
        
    async def connect(self):
        """Connect to Deriv WebSocket API"""
        uri = f"wss://ws.derivws.com/websockets/v3?app_id={self.app_id}"
        
        try:
            logger.info(f"Connecting to Deriv WebSocket: {uri}")
            self.websocket = await websockets.connect(uri)
            logger.info("✅ Connected to Deriv WebSocket successfully!")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to connect: {e}")
            return False
    
    async def subscribe_to_ticks(self):
        """Subscribe to tick stream for the specified symbol"""
        if not self.websocket:
            logger.error("❌ WebSocket not connected")
            return False
            
        try:
            # Subscribe to tick stream
            subscribe_request = {
                "ticks": self.symbol,
                "subscribe": 1
            }
            
            await self.websocket.send(json.dumps(subscribe_request))
            logger.info(f"📡 Subscribed to {self.symbol} tick stream")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to subscribe: {e}")
            return False
    
    async def send_ping(self):
        """Send periodic ping to keep connection alive"""
        while self.running and self.websocket:
            try:
                await asyncio.sleep(30)  # Ping every 30 seconds
                if self.websocket and not self.websocket.closed:
                    ping_msg = {"ping": 1}
                    await self.websocket.send(json.dumps(ping_msg))
                    logger.debug("🏓 Ping sent")
            except Exception as e:
                logger.error(f"❌ Ping failed: {e}")
                break
    
    async def listen_for_ticks(self):
        """Listen for incoming tick data"""
        if not self.websocket:
            logger.error("❌ WebSocket not connected")
            return
            
        try:
            logger.info(f"🎯 Listening for {self.symbol} tick data...")
            logger.info("Press Ctrl+C to stop")
            
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.process_message(data)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Failed to parse JSON: {e}")
                except Exception as e:
                    logger.error(f"❌ Error processing message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("⚠️ WebSocket connection closed")
        except Exception as e:
            logger.error(f"❌ Error in listen loop: {e}")
    
    async def process_message(self, data):
        """Process incoming WebSocket messages"""
        if 'tick' in data:
            # Handle tick data
            tick = data['tick']
            timestamp = datetime.fromtimestamp(tick['epoch'])
            
            logger.info("=" * 50)
            logger.info(f"📊 TICK DATA RECEIVED")
            logger.info(f"Symbol: {tick['symbol']}")
            logger.info(f"Price: {tick['quote']}")
            logger.info(f"Time: {timestamp}")
            logger.info(f"Epoch: {tick['epoch']}")
            if 'pip_size' in tick:
                logger.info(f"Pip Size: {tick['pip_size']}")
            logger.info("=" * 50)
            
        elif 'subscription' in data:
            # Handle subscription confirmation
            sub = data['subscription']
            logger.info(f"✅ Subscription confirmed for {sub['id']}")
            
        elif 'pong' in data:
            # Handle pong response
            logger.debug("🏓 Pong received")
            
        elif 'error' in data:
            # Handle errors
            error = data['error']
            logger.error(f"❌ API Error: {error['message']} (Code: {error['code']})")
            
        else:
            # Handle other messages
            logger.debug(f"📨 Other message: {data}")
    
    async def start(self):
        """Start the tick subscriber"""
        self.running = True
        
        # Connect to WebSocket
        if not await self.connect():
            return False
        
        # Subscribe to ticks
        if not await self.subscribe_to_ticks():
            return False
        
        # Start ping task
        self.ping_task = asyncio.create_task(self.send_ping())
        
        # Listen for tick data
        await self.listen_for_ticks()
        
        return True
    
    async def stop(self):
        """Stop the tick subscriber"""
        logger.info("🛑 Stopping tick subscriber...")
        self.running = False
        
        if self.ping_task:
            self.ping_task.cancel()
        
        if self.websocket:
            await self.websocket.close()
            logger.info("✅ WebSocket connection closed")

async def main():
    # Your Deriv API credentials from memory
    APP_ID = "71058"
    SYMBOL = "stpRNG"  # stepRNG 100
    
    logger.info("🚀 Starting Deriv stepRNG 100 Tick Subscriber")
    logger.info(f"App ID: {APP_ID}")
    logger.info(f"Symbol: {SYMBOL}")
    
    # Create subscriber instance
    subscriber = DerivTickSubscriber(APP_ID, SYMBOL)
    
    # Handle Ctrl+C gracefully
    def signal_handler(signum, frame):
        logger.info("\n🛑 Interrupt received, shutting down...")
        asyncio.create_task(subscriber.stop())
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Start the subscriber
        await subscriber.start()
    except KeyboardInterrupt:
        logger.info("\n🛑 Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
    finally:
        await subscriber.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
