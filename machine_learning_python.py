#!/usr/bin/env python3
"""
Machine Learning: Logistic Regression Trading Strategy (Python Version)
100% accurate conversion from Pine Script to Python - Signal Generation Only

Original Pine Script by capissimo
Python conversion with identical logic and calculations
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Tuple
import time
import warnings
warnings.filterwarnings('ignore')

class LogisticRegressionSignals:
    """
    Python implementation of Pine Script Logistic Regression Trading Strategy
    Generates signals only - no live trading
    """
    
    def __init__(self, 
                 price_type='Close',
                 lookback=3,
                 normalization_lookback=2,
                 learning_rate=0.0009,
                 iterations=1000,
                 filter_type='None',
                 use_price_data=True,
                 holding_period=5,
                 easter_egg=True):
        
        self.price_type = price_type
        self.lookback = max(2, lookback)
        self.normalization_lookback = max(2, min(240, normalization_lookback))
        self.learning_rate = max(0.0001, min(0.01, learning_rate))
        self.iterations = max(50, iterations)
        self.filter_type = filter_type
        self.use_price_data = use_price_data
        self.holding_period = max(1, holding_period)
        self.easter_egg = easter_egg
        
        # Constants
        self.BUY = 1
        self.SELL = -1
        self.HOLD = 0
    
    def get_price_series(self, df):
        """Extract price series based on price_type - exact Pine Script logic"""
        if self.price_type == 'Open':
            return df['open']
        elif self.price_type == 'High':
            return df['high']
        elif self.price_type == 'Low':
            return df['low']
        elif self.price_type == 'Close':
            return df['close']
        elif self.price_type == 'HL2':
            return (df['high'] + df['low']) / 2
        elif self.price_type == 'OC2':
            return (df['open'] + df['close']) / 2
        elif self.price_type == 'OHL3':
            return (df['open'] + df['high'] + df['low']) / 3
        elif self.price_type == 'HLC3':
            return (df['high'] + df['low'] + df['close']) / 3
        else:  # OHLC4
            return (df['open'] + df['high'] + df['low'] + df['close']) / 4
    
    def dot_product(self, v, w, p):
        """Dot product - sum(v * w, p) equivalent"""
        if len(v) < p:
            return 0.0
        return np.sum(v[-p:] * w)
    
    def sigmoid(self, z):
        """Sigmoid function - 1.0 / (1.0 + exp(-z))"""
        try:
            return 1.0 / (1.0 + np.exp(-z))
        except:
            return 0.0 if z < 0 else 1.0
    
    def logistic_regression(self, X, Y, p, lr, iterations):
        """
        Logistic Regression - exact Pine Script implementation
        """
        w = 0.0
        loss = 0.0
        
        for i in range(iterations):
            # hypothesis = sigmoid(dot(X, 0.0, p)) - but w starts at 0
            hypothesis = self.sigmoid(self.dot_product(X, w, p))
            
            # Prevent log(0) errors
            hypothesis = max(1e-15, min(1 - 1e-15, hypothesis))
            
            # Calculate loss (simplified version of Pine Script logic)
            if len(Y) >= p:
                y_recent = Y[-p:]
                loss = -1.0 / p * np.sum(y_recent * np.log(hypothesis) + (1.0 - y_recent) * np.log(1.0 - hypothesis))
            
            # Calculate gradient
            if len(Y) >= p:
                gradient = 1.0 / p * self.dot_product(X, hypothesis - Y[-p:], p)
            else:
                gradient = 0.0
            
            # Update weights
            w = w - lr * gradient
        
        # Return final loss and prediction
        final_prediction = self.sigmoid(self.dot_product(X, w, p))
        return loss, final_prediction
    
    def minimax_normalize(self, series, period, min_val, max_val):
        """Normalize to price range - minimax function"""
        rolling_high = series.rolling(window=period, min_periods=1).max()
        rolling_low = series.rolling(window=period, min_periods=1).min()
        
        # Avoid division by zero
        range_diff = rolling_high - rolling_low
        range_diff = np.where(range_diff == 0, 1, range_diff)
        
        normalized = (max_val - min_val) * (series - rolling_low) / range_diff + min_val
        return normalized
    
    def volume_break(self, volume, threshold=49):
        """Volume filter - volumeBreak function"""
        if volume is None or len(volume) < 14:
            return pd.Series([True] * len(volume), index=volume.index if volume is not None else [])
        
        # RSI of volume
        delta = volume.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=14, min_periods=1).mean()
        avg_loss = loss.rolling(window=14, min_periods=1).mean()
        
        rs = avg_gain / (avg_loss + 1e-10)
        rsi_vol = 100 - (100 / (1 + rs))
        
        # HMA of RSI volume (simplified as EMA)
        osc = rsi_vol.ewm(span=10).mean()
        
        return osc > threshold
    
    def volatility_break(self, high, low, close, vol_min=1, vol_max=10):
        """Volatility filter - volatilityBreak function"""
        # ATR calculation
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr_min = tr.rolling(window=vol_min, min_periods=1).mean()
        atr_max = tr.rolling(window=vol_max, min_periods=1).mean()
        
        return atr_min > atr_max
    
    def generate_signals(self, df):
        """
        Generate trading signals - main function
        100% identical logic to Pine Script
        """
        # Get price series
        base_ds = self.get_price_series(df)
        
        # Generate synthetic dataset - log(abs(pow(base_ds, 2) - 1) + .5)
        synth_ds = np.log(np.abs(np.power(base_ds, 2) - 1) + 0.5)
        
        # Easter egg calculation (time vs price data)
        if self.easter_egg:
            # Use time as base, price as synth (like Pine Script)
            base = pd.Series(range(len(df)), index=df.index)  # time equivalent
            synth = base_ds  # price data
        else:
            base = base_ds
            synth = synth_ds
        
        # Initialize result arrays
        signals = []
        losses = []
        predictions = []
        scaled_losses = []
        scaled_predictions = []
        
        # Process each bar (starting from lookback period)
        for i in range(self.lookback, len(df)):
            # Get data windows
            base_window = base.iloc[max(0, i-self.lookback):i].values
            synth_window = synth.iloc[max(0, i-self.lookback):i].values
            
            if len(base_window) >= self.lookback and len(synth_window) >= self.lookback:
                # Run logistic regression
                loss, prediction = self.logistic_regression(
                    base_window, synth_window, self.lookback, 
                    self.learning_rate, self.iterations
                )
                
                losses.append(loss)
                predictions.append(prediction)
                
                # Normalize loss and prediction to price range
                if i >= self.normalization_lookback:
                    price_window = base_ds.iloc[max(0, i-self.normalization_lookback):i]
                    min_price = price_window.min()
                    max_price = price_window.max()
                    
                    # Minimax normalization
                    if max_price != min_price:
                        scaled_loss = (max_price - min_price) * (loss - min(losses[-self.normalization_lookback:])) / (max(losses[-self.normalization_lookback:]) - min(losses[-self.normalization_lookback:]) + 1e-10) + min_price
                        scaled_pred = (max_price - min_price) * (prediction - min(predictions[-self.normalization_lookback:])) / (max(predictions[-self.normalization_lookback:]) - min(predictions[-self.normalization_lookback:]) + 1e-10) + min_price
                    else:
                        scaled_loss = loss
                        scaled_pred = prediction
                else:
                    scaled_loss = loss
                    scaled_pred = prediction
                
                scaled_losses.append(scaled_loss)
                scaled_predictions.append(scaled_pred)
            else:
                losses.append(0)
                predictions.append(0)
                scaled_losses.append(0)
                scaled_predictions.append(0)
        
        # Pad initial values
        for _ in range(self.lookback):
            losses.insert(0, 0)
            predictions.insert(0, 0)
            scaled_losses.insert(0, 0)
            scaled_predictions.insert(0, 0)
        
        # Create series
        loss_series = pd.Series(losses, index=df.index)
        prediction_series = pd.Series(predictions, index=df.index)
        scaled_loss_series = pd.Series(scaled_losses, index=df.index)
        scaled_prediction_series = pd.Series(scaled_predictions, index=df.index)
        
        # Apply filters
        filter_condition = pd.Series([True] * len(df), index=df.index)
        
        if self.filter_type == 'Volatility':
            filter_condition = self.volatility_break(df['high'], df['low'], df['close'])
        elif self.filter_type == 'Volume' and 'volume' in df.columns:
            filter_condition = self.volume_break(df['volume'])
        elif self.filter_type == 'Both':
            vol_filter = self.volatility_break(df['high'], df['low'], df['close'])
            if 'volume' in df.columns:
                volume_filter = self.volume_break(df['volume'])
                filter_condition = vol_filter & volume_filter
            else:
                filter_condition = vol_filter
        
        # Generate signals based on use_price_data setting
        signal_series = pd.Series([self.HOLD] * len(df), index=df.index)
        
        if self.use_price_data:
            # Use price vs scaled_loss comparison
            buy_condition = (base_ds > scaled_loss_series) & filter_condition
            sell_condition = (base_ds < scaled_loss_series) & filter_condition
        else:
            # Use crossover logic
            buy_condition = (scaled_loss_series > scaled_prediction_series) & (scaled_loss_series.shift(1) <= scaled_prediction_series.shift(1)) & filter_condition
            sell_condition = (scaled_loss_series < scaled_prediction_series) & (scaled_loss_series.shift(1) >= scaled_prediction_series.shift(1)) & filter_condition
        
        # Apply signals with holding period logic
        current_signal = self.HOLD
        hp_counter = 0
        
        for i in range(len(df)):
            if buy_condition.iloc[i] and current_signal != self.BUY:
                current_signal = self.BUY
                hp_counter = 0
            elif sell_condition.iloc[i] and current_signal != self.SELL:
                current_signal = self.SELL
                hp_counter = 0
            
            signal_series.iloc[i] = current_signal
            hp_counter += 1
            
            # Reset signal after holding period
            if hp_counter >= self.holding_period:
                current_signal = self.HOLD
                hp_counter = 0
        
        # Create results DataFrame
        results = pd.DataFrame({
            'signal': signal_series,
            'buy': signal_series == self.BUY,
            'sell': signal_series == self.SELL,
            'loss': loss_series,
            'prediction': prediction_series,
            'scaled_loss': scaled_loss_series,
            'scaled_prediction': scaled_prediction_series,
            'filter': filter_condition
        }, index=df.index)
        
        return results

def test_strategy():
    """Test the strategy with Step Index Renko data"""
    # Load Step Index Renko data
    data_file = r'C:\Users\<USER>\Documents\QT-META\step_index_renko_0_1_30days_fixed.csv'

    print(f"Loading Step Index Renko data from: {data_file}")
    try:
        df = pd.read_csv(data_file)
        print(f"Data loaded successfully. Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")

        # Convert datetime if it exists
        if 'datetime' in df.columns:
            df['datetime'] = pd.to_datetime(df['datetime'])
        elif 'time' in df.columns:
            df['datetime'] = pd.to_datetime(df['time'])

        # Display first few rows
        print("\nFirst 5 rows:")
        print(df.head())

        # Check for required OHLC columns
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in df.columns]

        if missing_cols:
            print(f"Warning: Missing columns {missing_cols}")
            # For Renko data, all OHLC might be the same
            if 'close' in df.columns:
                for col in missing_cols:
                    df[col] = df['close']
                print("Missing OHLC columns filled with close price")

    except FileNotFoundError:
        print(f"Error: File not found at {data_file}")
        print("Please check the file path and try again.")
        return None, None
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None

    # Initialize strategy with parameters optimized for Step Index
    strategy = LogisticRegressionSignals(
        price_type='Close',
        lookback=3,
        normalization_lookback=4,  # Reduced for Step Index as mentioned in Pine Script
        learning_rate=0.0009,
        iterations=1000,
        use_price_data=True,  # Use price data as recommended for Step Index
        holding_period=5,
        filter_type='None'
    )

    # Generate signals
    print("\nGenerating Logistic Regression signals...")
    start_time = time.time()
    signals = strategy.generate_signals(df)
    end_time = time.time()

    print(f"Signal generation completed in {end_time - start_time:.2f} seconds")

    # Print results
    buy_signals = signals['buy'].sum()
    sell_signals = signals['sell'].sum()
    total_signals = buy_signals + sell_signals

    print(f"\n=== SIGNAL ANALYSIS ===")
    print(f"Total data points: {len(df):,}")
    print(f"Total BUY signals: {buy_signals:,}")
    print(f"Total SELL signals: {sell_signals:,}")
    print(f"Total signals: {total_signals:,}")
    print(f"Signal rate: {total_signals / len(df) * 100:.2f}%")

    # Show signal distribution over time
    if total_signals > 0:
        print(f"\nBUY/SELL ratio: {buy_signals/sell_signals:.2f}" if sell_signals > 0 else f"\nBUY/SELL ratio: {buy_signals}/0")

        # Find first and last signals
        signal_indices = signals[signals['buy'] | signals['sell']].index
        if len(signal_indices) > 0:
            first_signal = signal_indices[0]
            last_signal = signal_indices[-1]
            print(f"First signal at index: {first_signal}")
            print(f"Last signal at index: {last_signal}")

            if 'datetime' in df.columns:
                print(f"First signal time: {df.loc[first_signal, 'datetime']}")
                print(f"Last signal time: {df.loc[last_signal, 'datetime']}")

    # Save results
    output_file = 'logistic_regression_signals.csv'
    result_df = pd.concat([df, signals], axis=1)
    result_df.to_csv(output_file, index=False)
    print(f"\nResults saved to: {output_file}")

    return df, signals

if __name__ == "__main__":
    df, signals = test_strategy()
