{"timestamp": "20250607_183107", "training_samples": 104131, "test_samples": 26033, "features": ["price", "price_change", "price_pct_change", "direction", "hour", "minute", "day_of_week", "up_streak", "down_streak", "price_momentum_5", "price_pct_change_5", "up_count_5", "down_count_5", "volatility_5", "max_up_streak_5", "max_down_streak_5", "price_momentum_10", "price_pct_change_10", "up_count_10", "down_count_10", "volatility_10", "max_up_streak_10", "max_down_streak_10", "price_momentum_15", "price_pct_change_15", "up_count_15", "down_count_15", "volatility_15", "max_up_streak_15", "max_down_streak_15", "price_momentum_20", "price_pct_change_20", "up_count_20", "down_count_20", "volatility_20", "max_up_streak_20", "max_down_streak_20", "price_momentum_30", "price_pct_change_30", "up_count_30", "down_count_30", "volatility_30", "max_up_streak_30", "max_down_streak_30", "xATRTrailingStop", "pos", "buy", "sell", "barbuy", "barsell", "xATR", "ema_src"], "feature_count": 52, "utbot_features": ["xATRTrailingStop", "pos", "buy", "sell", "barbuy", "barsell", "xATR", "ema_src"], "accuracy": 0.60377213536665, "precision": 0.6037411781245623, "recall": 0.60377213536665, "f1_score": 0.6037274668287015, "training_time_seconds": 7.484200599999895, "xgb_params": {"objective": "binary:logistic", "max_depth": 6, "learning_rate": 0.1, "n_estimators": 200, "subsample": 0.8, "colsample_bytree": 0.8, "random_state": 42, "n_jobs": -1, "eval_metric": "logloss"}, "data_source": "step_index_renko_0_1_3days.csv", "target_lookahead_periods": 3}